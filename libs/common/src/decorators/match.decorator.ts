import { registerDecorator, ValidationArguments, ValidationOptions, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

@ValidatorConstraint({ name: 'Match' })
export class MatchConstraint implements ValidatorConstraintInterface {
    validate(value: any, args: ValidationArguments) {
        const [propertyName] = args.constraints;
        const relatedValue = (args.object as any)[propertyName];
        return value === relatedValue;
    }

    defaultMessage(args: ValidationArguments) {
        const [propertyName] = args.constraints;
        return `${args.property} must match ${propertyName}`;
    }
}

export function Match(property: string, validationOptions?: ValidationOptions) {
    return (object: any, propertyName: string) => {
        registerDecorator({
            target: object.constructor,
            propertyName,
            options: validationOptions,
            constraints: [property],
            validator: MatchConstraint,
        });
    };
}