// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v5.28.2
// source: integration-proto/track-rate.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty, Id, Meta, Query, Status } from "../common-proto/common";

export interface GetCustomTrackRateResponse {
  price?: number | undefined;
}

export interface DeleteTrackRateRequest {
  ids?: string[] | undefined;
}

export interface SetCustomTrackRateRequest {
  price?: number | undefined;
}

export interface CreateTrackRateRequest {
  trackId?: string | undefined;
  type?: string | undefined;
}

export interface CreateTrackRatesRequest {
  requests?: CreateTrackRateRequest[] | undefined;
}

export interface ListTrackRatesRequest {
  query?: Query | undefined;
}

export interface UpdateTrackRateRequest {
  id?: string | undefined;
  price?: number | undefined;
  type?: string | undefined;
  isActive?: boolean | undefined;
}

export interface Track {
  id?: string | undefined;
  key?: string | undefined;
  songTitle?: string | undefined;
  isrc?: string | undefined;
  credits?: string | undefined;
  albumName?: string | undefined;
  isDelete?: boolean | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface TrackRate {
  id?: string | undefined;
  price?: number | undefined;
  type?: string | undefined;
  isActive?: boolean | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  track?: Track | undefined;
}

export interface ListTrackRatesResponse {
  data?: TrackRate[] | undefined;
  meta?: Meta | undefined;
}

export interface GetTrackRateByIdsRequest {
  ids?: string[] | undefined;
}

export interface GetTrackRateByIdsResponse {
  data?: TrackRate[] | undefined;
}

export interface TrackRateServiceClient {
  createTrackRate(request: CreateTrackRatesRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  getTrackRate(request: Id, metadata: Metadata, ...rest: any): Observable<Status>;

  getTrackRateByIds(
    request: GetTrackRateByIdsRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetTrackRateByIdsResponse>;

  listTrackRates(request: ListTrackRatesRequest, metadata: Metadata, ...rest: any): Observable<ListTrackRatesResponse>;

  updateTrackRate(request: UpdateTrackRateRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  deleteTrackRate(request: DeleteTrackRateRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  setCustomTrackRate(request: SetCustomTrackRateRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  getCustomTrackRate(request: Empty, metadata: Metadata, ...rest: any): Observable<GetCustomTrackRateResponse>;
}

export interface TrackRateServiceController {
  createTrackRate(
    request: CreateTrackRatesRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  getTrackRate(request: Id, metadata: Metadata, ...rest: any): Promise<Status> | Observable<Status> | Status;

  getTrackRateByIds(
    request: GetTrackRateByIdsRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetTrackRateByIdsResponse> | Observable<GetTrackRateByIdsResponse> | GetTrackRateByIdsResponse;

  listTrackRates(
    request: ListTrackRatesRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListTrackRatesResponse> | Observable<ListTrackRatesResponse> | ListTrackRatesResponse;

  updateTrackRate(
    request: UpdateTrackRateRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  deleteTrackRate(
    request: DeleteTrackRateRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  setCustomTrackRate(
    request: SetCustomTrackRateRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  getCustomTrackRate(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetCustomTrackRateResponse> | Observable<GetCustomTrackRateResponse> | GetCustomTrackRateResponse;
}

export function TrackRateServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createTrackRate",
      "getTrackRate",
      "getTrackRateByIds",
      "listTrackRates",
      "updateTrackRate",
      "deleteTrackRate",
      "setCustomTrackRate",
      "getCustomTrackRate",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("TrackRateService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("TrackRateService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const TRACK_RATE_SERVICE_NAME = "TrackRateService";
