{"name": "lmkn", "version": "0.0.1", "prisma": {"seed": "ts-node prisma/seed.ts"}, "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/lmkn/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/lmkn/test/jest-e2e.json", "seed:postal": "ts-node prisma/seed.ts postal", "seed:global": "ts-node prisma/seed.ts global", "seed:lmk": "ts-node prisma/seed.ts lmk", "seed:creator": "ts-node prisma/seed.ts creator", "seed:unclaim": "ts-node prisma/seed.ts unclaim"}, "dependencies": {"@grpc/grpc-js": "^1.11.3", "@grpc/proto-loader": "^0.7.13", "@grpc/reflection": "^1.0.4", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.0.3", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.4", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.4", "@nestjs/cqrs": "^10.2.7", "@nestjs/elasticsearch": "^11.1.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/microservices": "^10.4.4", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.4", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^11.0.0", "@prisma/client": "^5.20.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "cache-manager": "5.7.6", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "express": "^4.21.0", "grpc": "^1.24.11", "handlebars": "^4.7.8", "minio": "^8.0.5", "moment": "^2.30.1", "nestjs-prisma": "^0.23.0", "nodemailer": "^6.9.15", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "preview-email": "^3.1.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "ua-parser-js": "^1.0.39"}, "devDependencies": {"@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.4", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.21", "@types/jest": "29.5.12", "@types/multer": "^1.4.12", "@types/node": "20.14.9", "@types/nodemailer": "^6.4.16", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "29.7.0", "prettier": "^3.3.3", "prisma": "^5.20.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "29.1.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "ts-proto": "^1.181.2", "tsconfig-paths": "4.2.0", "typescript": "^5.6.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/libs/", "<rootDir>/apps/"], "moduleNameMapper": {"^y/common(|/.*)$": "<rootDir>/libs/common/src/$1", "^y/validator(|/.*)$": "<rootDir>/libs/validator/src/$1"}}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@prisma/client", "@prisma/engines", "bcrypt", "prisma", "protobufjs"]}}