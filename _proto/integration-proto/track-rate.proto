syntax = "proto3";

package integration;

import "common-proto/common.proto";

service TrackRateService {
  rpc CreateTrackRate (CreateTrackRatesRequest) returns (common.Status) {}
  rpc GetTrackRate (common.Id) returns (common.Status) {}
  rpc GetTrackRateByIds (GetTrackRateByIdsRequest) returns (GetTrackRateByIdsResponse) {}
  rpc ListTrackRates (ListTrackRatesRequest) returns (ListTrackRatesResponse) {}
  rpc UpdateTrackRate (UpdateTrackRateRequest) returns (common.Status) {}
  rpc DeleteTrackRate (DeleteTrackRateRequest) returns (common.Status) {}
  rpc SetCustomTrackRate (SetCustomTrackRateRequest) returns (common.Status) {}
  rpc GetCustomTrackRate (common.Empty) returns (GetCustomTrackRateResponse) {}
}

message GetCustomTrackRateResponse {
  float price = 1;
}

message DeleteTrackRateRequest {
  repeated string ids = 1; 
}

message SetCustomTrackRateRequest {
  float price = 1;
}

message CreateTrackRateRequest {
  string trackId = 1;
  string type = 2;
}

message CreateTrackRatesRequest {
  repeated CreateTrackRateRequest requests = 1;
}

message ListTrackRatesRequest {
  common.Query query = 1;
}

message UpdateTrackRateRequest {
  string id = 1;
  float price = 2;
  string type = 3;
  bool isActive = 4;
}

message Track {
  string id = 1;
  string key = 2;
  string songTitle = 3;
  string isrc = 4;
  string credits = 5;
  string albumName = 6;
  bool isDelete = 7;
  string createdAt = 8;
  string updatedAt = 9;
}

message TrackRate {
  string id = 1;
  float price = 2;
  string type = 3;
  bool isActive = 4;
  string createdAt = 5;
  string updatedAt = 6;
  Track track = 7;
}

message ListTrackRatesResponse {
  repeated TrackRate data = 1;
  common.Meta meta = 2;
}

message GetTrackRateByIdsRequest {
  repeated string ids = 1;
}

message GetTrackRateByIdsResponse {
  repeated TrackRate data = 1;
}