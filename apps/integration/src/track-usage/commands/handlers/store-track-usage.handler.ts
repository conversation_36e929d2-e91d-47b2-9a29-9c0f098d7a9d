import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { HttpStatus, InternalServerErrorException } from '@nestjs/common';
import { StoreTrackUsageCommand } from '../impl';
import { UnclaimAsset } from '@prisma/client';

interface TrackUsageResponse {
  status: number;
  msg: string;
}

@CommandHandler(StoreTrackUsageCommand)
export class StoreTrackUsageHandler implements ICommandHandler<StoreTrackUsageCommand> {
  constructor(private prisma: PrismaService) { }

  async execute(command: StoreTrackUsageCommand): Promise<TrackUsageResponse> {
    try {
      const { args, dspId } = command;
      const track = await this.findTrack(args.isrc);

      if (track) {
        await this.handleExistingTrack(track, dspId, args);
      } else {
        await this.handleNewTrack(args, dspId);
      }

      return { status: HttpStatus.OK, msg: 'success store track usage' };
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('failed store track usage');
    }
  }

  private convertToMilliseconds(seconds: number): number {
    return seconds * 1000;
  }

  private async findTrack(isrc: string) {
    return this.prisma.track.findFirst({
      where: { isrc }
    });
  }

  private async handleExistingTrack(track: any, dspId: string, args: any) {
    if (!track.dspIds.includes(dspId)) {
      await this.updateTrackDspIds(track, dspId);
    }

    await this.updateTrackDetails(track, args);

    await this.createUnclaimAsset(args, dspId, track.isrc, track.id);
  }

  private async updateTrackDetails(track: any, args: any) {
    const updates: any = {};

    if (args.songTitle && args.songTitle !== track.songTitle) {
      updates.songTitle = args.songTitle;
    }
    if (args.singer && args.singer !== track.singer) {
      updates.singer = args.singer;
    }
    if (args.durationSong && args.durationSong !== track.duration) {
      updates.duration = args.durationSong;
    }
    if (args.album && args.album !== track.albumName) {
      updates.albumName = args.album;
    }
    if (args.upc && args.upc !== track.upc) {
      updates.upc = args.upc;
    }
    if (args.label && args.label !== track.label) {
      updates.label = args.label;
    }
    if (args.releaseDate && new Date(args.releaseDate).getTime() !== track.releaseDate?.getTime()) {
      updates.releaseDate = new Date(args.releaseDate);
    }
    if (args.country && args.country !== track.originCountry) {
      updates.originCountry = args.country;
    }

    if (args.credits) {
      try {
        updates.credits = JSON.parse(args.credits);
      } catch (e) {
        updates.credits = [];
      }
    }

    if (Object.keys(updates).length > 0) {
      await this.prisma.track.update({
        where: { isrc: track.isrc },
        data: updates
      });
    }
  }

  private async handleNewTrack(args: any, dspId: string) {
    const newTrack = await this.createTrack(args, dspId);
    await this.createUnclaimAsset(args, dspId, newTrack.isrc, newTrack.id);
  }

  private async updateTrackDspIds(track: any, dspId: string) {
    const updatedDspIds = [...track.dspIds, dspId];
    await this.prisma.track.update({
      where: { isrc: track.isrc },
      data: { dspIds: updatedDspIds }
    });
  }

  private async createTrack(args: any, dspId: string) {
    let credits: any = [];
    if (args.credits) {
      try {
        credits = JSON.parse(args.credits);
      } catch (e) {
        credits = [];
      }
    }
    return this.prisma.track.create({
      data: {
        isrc: args.isrc,
        songTitle: args.songTitle,
        singer: args.singer,
        duration: args.durationSong,
        albumName: args.album,
        upc: args.upc,
        label: args.label,
        releaseDate: args.releaseDate ? new Date(args.releaseDate) : undefined,
        dspIds: [dspId],
        originCountry: args.country,
        credits,
      },
    });
  }

  private async createUnclaimAsset(
    args: any,
    dspId: string,
    isrc: string,
    trackId: string
  ): Promise<UnclaimAsset> {
    const existingUnclaim = await this.prisma.unclaimAsset.findFirst({
      where: {
        AND: [
          { isrc },
          { dspId }
        ]
      }
    });

    if (existingUnclaim) {
      const createdAt = new Date(args.createdAt || Date.now());

      const existingPlayHistory = await this.prisma.unclaimAssetPlayHistory.findFirst({
        where: {
          unclaimAssetId: existingUnclaim.id,
          deviceId: args.deviceId,
          ipAddress: args.ip,
          createdAt: {
            gte: new Date(createdAt.getTime() - 5_000),
            lte: new Date(createdAt.getTime() + 5_000),
          }
        }
      });


      if (existingPlayHistory) {
        console.info('[UnclaimAssetPlayHistory] Duplicate detected — skipping insert', {
          isrc,
          deviceId: args.deviceId,
          ipAddress: args.ip,
          createdAt: args.createdAt || '[fallback: now]',
          dspId,
        });
        return existingUnclaim;
      }



      const updated = await this.prisma.unclaimAsset.update({
        where: { id: existingUnclaim.id },
        data: {
          playCount: {
            increment: args.playCount
          },
          playHistory: {
            create: {
              cid: args.cid,
              brandName: args.brandName,
              deviceId: args.deviceId,
              deviceName: args.deviceName,
              zoneName: args.zoneName,
              origin: args.origin,
              industry: args.industry,
              os: args.os,
              ipAddress: args.ip,
              propertyId: args.propertyId,
              duration: this.convertToMilliseconds(args.durationPlay),
              type: args.flag === 'VELODIVA' ? 'PAID': args.flag,
              createdAt: new Date(args.createdAt || Date.now()),
            }
          }
        }
      });
      return updated;
    }

    const unclaim = await this.prisma.unclaimAsset.create({
      data: {
        trackId,
        dspId,
        songTitle: args.songTitle,
        singer: args.singer,
        duration: args.durationSong,
        album: args.album,
        upc: args.upc,
        label: args.label,
        releaseDate: args.releaseDate ? new Date(args.releaseDate) : undefined,
        isrc: args.isrc,
        playCount: args.playCount,
        country: args.country,
        playHistory: {
          create: {
            cid: args.cid,
            brandName: args.brandName,
            deviceId: args.deviceId,
            deviceName: args.deviceName,
            zoneName: args.zoneName,
            origin: args.origin,
            industry: args.industry,
            os: args.os,
            ipAddress: args.ip,
            propertyId: args.propertyId,
            duration: this.convertToMilliseconds(args.durationPlay),
            type: args.flag === 'VELODIVA' ? 'PAID': args.flag,
            createdAt: new Date(args.createdAt || Date.now()),
          }
        }
      }
    });

    return unclaim;
  }
}
