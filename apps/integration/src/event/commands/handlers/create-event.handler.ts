import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>andler } from '@nestjs/cqrs';
import { CreateEventCommand } from '../impl/create-event.command';
import { PrismaService } from 'nestjs-prisma';
import { BadRequestException, Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';

@CommandHandler(CreateEventCommand)
@Injectable()
export class CreateEventHandler implements ICommandHandler<CreateEventCommand> {
    constructor(
        private readonly prisma: PrismaService,
        private readonly elastic: ElasticsearchService
    ) { }

    async execute(command: CreateEventCommand) {
        const { data } = command;

        const eventCategory = await this.prisma.eventCategory.findFirst({
            where: {
                code: data.categoryCode
            }
        });

        if (!eventCategory) {
            throw new NotFoundException('Event category not found');
        }

        try {
            await this.prisma.$transaction(async (tx) => {
                const processedSongs = await Promise.all(data.songs.map(async (song) => {
                    const esParams: any = {
                        index: 'gmi.tracks',
                        search_type: 'dfs_query_then_fetch',
                        _source: {
                            includes: [
                                'id',
                                'title',
                                'artists',
                                'isrc',
                                'credits',
                                'album',
                            ],
                        },
                        query: {
                            bool: {
                                must: [
                                    { term: { _id: song.trackId } }
                                ]
                            }
                        }
                    };

                    const esResult = await this.elastic.search(esParams);
                    const trackFromEs = esResult.hits.hits[0]?._source as any;

                    if (!trackFromEs) {
                        throw new NotFoundException(`Track with ID ${song.trackId} not found in Elasticsearch`);
                    }

                    let track = await tx.track.findUnique({
                        where: { isrc: trackFromEs.isrc }
                    });

                    if (!track) {
                        track = await tx.track.create({
                            data: {
                                key: trackFromEs.id,
                                songTitle: trackFromEs.title,
                                isrc: trackFromEs.isrc,
                                isDelete: false,
                                credits: trackFromEs.credits,
                                albumName: trackFromEs.album.name,
                            }
                        });
                    }

                    return { trackId: track.id };
                }));

                const event = await tx.event.create({
                    data: {
                        name: data.name,
                        type: data.type,
                        promoter: data.promoter,
                        organizer: data.organizer,
                        startDate: new Date(data.startDate),
                        endDate: new Date(data.endDate),
                        durationDays: data.durationDays,
                        mainVenue: data.mainVenue,
                        subVenue: data.subVenue,
                        basedOn: data.basedOn,
                        complimentary: data.complimentary,
                        commercial: data.commercial,
                        royaltyRevenue: data.royaltyRevenue,
                        categoryId: eventCategory.id,
                        license: {
                            create: {
                                number: data.license.number,
                                certificateNumber: data.license.certificateNumber,
                                releaseDate: data.license.releaseDate ? new Date(data.license.releaseDate) : null,
                                approvedBy: data.license.approvedBy,
                                approvedAt: data.license.approvedAt ? new Date(data.license.approvedAt) : null,
                                expiredAt: data.license.expiredAt ? new Date(data.license.expiredAt) : null,
                                status: data.license.status
                            }
                        },
                        EventSong: {
                            create: processedSongs
                        },
                        EventRevenues: {
                            create: data.revenues.map(revenue => ({
                                ticketAgent: revenue.ticketAgent,
                                ticketCategory: revenue.ticketCategory,
                                ticketPrice: revenue.ticketPrice,
                                complimentaryQty: revenue.complimentaryQty,
                                complimentaryRev: revenue.complimentaryRev,
                                commercialQty: revenue.commercialQty,
                                commercialRev: revenue.commercialRev
                            }))
                        },
                    },
                    include: {
                        license: true,
                        EventSong: true,
                        EventRevenues: true,
                    }
                });
                return event;
            });

            return { success: true, message: 'Event created successfully' };
            
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new InternalServerErrorException(error?.message || 'Internal server error');
        }
    }
}