import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { <PERSON>Array, IsNotEmpty, IsNumber, IsString, ValidateNested } from "class-validator";

export class EventLicenseDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    number: string;
    
    @ApiProperty()
    @IsString()
    certificateNumber: string;

    @ApiProperty()
    @IsString()
    releaseDate: string;

    @ApiProperty()
    @IsString()
    approvedBy: string;

    @ApiProperty()
    @IsString()
    approvedAt: string;

    @ApiProperty()
    @IsString()
    expiredAt: string;

    @ApiProperty()
    @IsString()
    status: string;
}

export class EventSongDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    trackId: string;
}

export class EventRevenueDto {
    @ApiProperty()
    @IsString()
    ticketAgent: string;

    @ApiProperty()
    @IsString()
    ticketCategory: string;

    @ApiProperty()
    @IsNumber()
    ticketPrice: number;

    @ApiProperty()
    @IsNumber()
    complimentaryQty: number;

    @ApiProperty()
    @IsNumber()
    complimentaryRev: number;

    @ApiProperty()
    @IsNumber()
    commercialQty: number;

    @ApiProperty()
    @IsNumber()
    commercialRev: number;
}

export class CreateEventDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    type: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    promoter: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    organizer: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    startDate: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    endDate: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsNumber()
    durationDays: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    mainVenue: string;

    @ApiProperty()
    @IsString()
    subVenue: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    basedOn: string;

    @ApiProperty()
    @IsNumber()
    complimentary: number;

    @ApiProperty()
    @IsNumber()
    commercial: number;

    @ApiProperty()
    @IsNumber()
    royaltyRevenue: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    categoryCode: string;

    @ApiProperty({ type: EventLicenseDto })
    @ValidateNested()
    @Type(() => EventLicenseDto)
    license: EventLicenseDto;

    @ApiProperty({ type: [EventSongDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => EventSongDto)
    songs: EventSongDto[];

    @ApiProperty({ type: [EventRevenueDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => EventRevenueDto)
    revenues: EventRevenueDto[];
}