import { Modu<PERSON> } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { EventCommandHandlers } from './commands';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventController } from './event.controller';

@Module({
  imports: [
    CqrsModule,
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => ({
          node: config.get<string>('ELASTIC_NODE'),
      }),
      inject: [ConfigService],
  }),
  ],
  providers: [...EventCommandHandlers],
  controllers: [EventController],
})
export class EventModule {}
