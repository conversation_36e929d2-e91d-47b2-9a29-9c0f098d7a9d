import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { CreateEventCommand } from './commands/impl/create-event.command';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { CreateEventDto } from './dto/create-event.dto';


@Controller('event')
@ApiTags('Event')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class EventController {
  constructor(private readonly commandBus: CommandBus) { }

  @Post()
  createEvent(@Body() data: CreateEventDto) {
    return this.commandBus.execute(new CreateEventCommand(data))
  }
}
