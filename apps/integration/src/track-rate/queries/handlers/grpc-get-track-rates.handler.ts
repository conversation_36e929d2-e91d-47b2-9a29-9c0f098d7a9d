import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetTrackRatesQuery } from '../impl';
import { convertKeyValuePairs } from '@app/common';

@QueryHandler(GrpcGetTrackRatesQuery)
export class GrpcGetTrackRatesHandler implements IQueryHandler<GrpcGetTrackRatesQuery> {
    constructor(private readonly prisma: PrismaService) { }

    async execute(query: GrpcGetTrackRatesQuery) {
        const { args } = query;
        const params = convertKeyValuePairs(args?.filters || []);
        const page = args?.page || 1;
        const limit = args?.limit || 10;
        const offset = (page - 1) * limit;

        try {
            const where: any = {};

            if (params.search) {
                where.OR = [
                    {
                        track: {
                            songTitle: { contains: params.search, mode: 'insensitive' }
                        }
                    },
                    {
                        track: {
                            isrc: { contains: params.search, mode: 'insensitive' }
                        }
                    }
                ];
            }

            if (params.keys) {
                const keys = Array.isArray(params.keys) ? params.keys : [params.keys];
                where.track = {
                    ...where.track,
                    key: { in: keys }
                };
            }

            if (params.type) {
                where.type = params.type;
            }

            if (params.isActive !== undefined) {
                where.isActive = params.isActive === 'true';
            }

            const [data, total] = await this.prisma.$transaction([
                this.prisma.trackRate.findMany({
                    where: {
                        isDelete: false,
                        ...where
                    },
                    include: {
                        track: {
                            select: {
                                id: true,
                                key: true,
                                songTitle: true,
                                isrc: true,
                                credits: true,
                                albumName: true,
                                isDelete: true,
                                createdAt: true,
                                updatedAt: true
                            }
                        }
                    },
                    skip: offset,
                    take: limit,
                    orderBy: { createdAt: 'desc' }
                }),
                this.prisma.trackRate.count({ where })
            ]);

            const lastPage = Math.ceil(total / limit);

            const mapped = data.map(item => ({
                id: item.id,
                price: item.price,
                type: item.type,
                isActive: item.isActive,
                createdAt: item.createdAt?.toISOString() ?? null,
                updatedAt: item.updatedAt?.toISOString() ?? null,
                track: item.track ? {
                    id: item.track.id,
                    key: item.track.key,
                    songTitle: item.track.songTitle,
                    isrc: item.track.isrc,
                    credits: item.track.credits,
                    albumName: item.track.albumName,
                    isDelete: item.track.isDelete,
                    createdAt: item.track.createdAt?.toISOString() ?? null,
                    updatedAt: item.track.updatedAt?.toISOString() ?? null
                } : null
            }));

            return {
                data: mapped,
                meta: {
                    total,
                    lastPage,
                    currentPage: page,
                    limit
                }
            };

        } catch (error) {
            throw new RpcException(error);
        }
    }
}