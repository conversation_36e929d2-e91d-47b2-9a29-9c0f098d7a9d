import { <PERSON><PERSON>ueryHand<PERSON> } from "@nestjs/cqrs";
import { GrpcGetCustomTrackRateQuery } from "../impl/grpc-get-custom-track-rate.query";
import { PrismaService } from "nestjs-prisma";
import { RpcException } from "@nestjs/microservices";
import { Status } from "@grpc/grpc-js/build/src/constants";

export class GrpcGetCustomTrackRateHandler implements IQueryHandler<GrpcGetCustomTrackRateQuery> {
    constructor(
        private readonly prisma: PrismaService
    ) { }

    async execute(query: GrpcGetCustomTrackRateQuery): Promise<any> {
        const item = await this.prisma.setting.findFirst({
            where: { type: 'track-rate' },
        });

        if (!item) {
            throw new RpcException({
                code: Status.NOT_FOUND,
                message: 'Track rate not found',
            });
        }

        return {
            price: item.option['rate'],
        };
    }
}