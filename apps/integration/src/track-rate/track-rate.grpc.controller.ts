import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
    TRACK_RATE_SERVICE_NAME,
    UpdateTrackRateRequest,
    GetTrackRateByIdsRequest,
    CreateTrackRatesRequest,
    DeleteTrackRateRequest,
    SetCustomTrackRateRequest,
    GetCustomTrackRateResponse
} from 'libs/proto-schema/src/index.integration';
import { common } from 'libs/proto-schema/src';
import {
    GrpcCreateTrackRateCommand,
    GrpcUpdateTrackRateCommand,
    GrpcDeleteTrackRateCommand,
    GrpcSetCustomTrackRateCommand
} from './commands/impl';
import {
    GrpcGetTrackRateQuery,
    GrpcGetTrackRatesQuery,
    GrpcGetTrackRateByIdsQuery,
    GrpcGetCustomTrackRateQuery
} from './queries/impl';

@Controller()
export class TrackRateGrpcController {
    constructor(
        private readonly commandBus: CommandBus,
        private readonly queryBus: QueryBus
    ) { }

    @GrpcMethod(TRACK_RATE_SERVICE_NAME, 'CreateTrackRate')
    createTrackRate(data: CreateTrackRatesRequest) {
        return this.commandBus.execute(new GrpcCreateTrackRateCommand(data));
    }

    @GrpcMethod(TRACK_RATE_SERVICE_NAME, 'GetTrackRate')
    getTrackRate(id: common.Id) {
        return this.queryBus.execute(new GrpcGetTrackRateQuery(id));
    }

    @GrpcMethod(TRACK_RATE_SERVICE_NAME, 'GetTrackRateByIds')
    getTrackRateByIds(request: GetTrackRateByIdsRequest) {
        return this.queryBus.execute(new GrpcGetTrackRateByIdsQuery(request));
    }

    @GrpcMethod(TRACK_RATE_SERVICE_NAME, 'ListTrackRates')
    listTrackRates(query: common.Query) {
        return this.queryBus.execute(new GrpcGetTrackRatesQuery(query));
    }

    @GrpcMethod(TRACK_RATE_SERVICE_NAME, 'UpdateTrackRate')
    updateTrackRate(data: UpdateTrackRateRequest) {
        return this.commandBus.execute(new GrpcUpdateTrackRateCommand(data));
    }

    @GrpcMethod(TRACK_RATE_SERVICE_NAME, 'DeleteTrackRate')
    deleteTrackRate(args: DeleteTrackRateRequest) {
        return this.commandBus.execute(new GrpcDeleteTrackRateCommand(args));
    }

    @GrpcMethod(TRACK_RATE_SERVICE_NAME, 'SetCustomTrackRate')
    setCustomTrackRate(data: SetCustomTrackRateRequest) {
        return this.commandBus.execute(new GrpcSetCustomTrackRateCommand(data));
    }

    @GrpcMethod(TRACK_RATE_SERVICE_NAME, 'GetCustomTrackRate')
    getCustomTrackRate(data: common.Empty): Promise<GetCustomTrackRateResponse> {
        return this.queryBus.execute(new GrpcGetCustomTrackRateQuery());
    }
}