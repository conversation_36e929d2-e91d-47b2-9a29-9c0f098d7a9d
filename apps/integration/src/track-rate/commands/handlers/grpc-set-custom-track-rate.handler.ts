import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GrpcSetCustomTrackRateCommand } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { RpcException } from "@nestjs/microservices";
import { Status } from "@grpc/grpc-js/build/src/constants";

export class GrpcSetCustomTrackRateHandler implements ICommandHandler<GrpcSetCustomTrackRateCommand> {
    constructor(
        private readonly prisma: PrismaService
    ) { }

    async execute(command: GrpcSetCustomTrackRateCommand) {
        const { request } = command

        try {
            return await this.prisma.$transaction(async (tx) => {
                await tx.setting.upsert({
                    where: { type: 'track-rate' },
                    create: {
                        type: 'track-rate',
                        option: {
                            ...{
                                rate: request.price
                            }
                        },
                    },
                    update: {
                        option: {
                            ...{
                                rate: request.price
                            }
                        },
                    },
                });

                await tx.trackRate.updateMany({
                    where: {
                        isDelete: false,
                    },
                    data: {
                        price: request.price,
                    },
                });

                return new RpcException({
                    code: Status.OK,
                    message: 'Track rate created successfully'
                })
            });
        } catch (error) {
            if (error instanceof RpcException) {
                throw error;
            }
            throw new RpcException({
                code: Status.INTERNAL,
                message: error.message
            });
        }
    }
}