import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GrpcCreateTrackRateCommand } from '../impl/grpc-create-track-rate.command';
import { Injectable } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { RpcException } from '@nestjs/microservices';
import { Status } from '@grpc/grpc-js/build/src/constants';

@CommandHandler(GrpcCreateTrackRateCommand)
@Injectable()
export class GrpcCreateTrackRateHandler implements ICommandHandler<GrpcCreateTrackRateCommand> {
    constructor(
        private readonly prisma: PrismaService,
        private readonly elasticService: ElasticsearchService,
    ) { }

    async execute(command: GrpcCreateTrackRateCommand) {
        const { data } = command;
        const trackIds = data.requests.map(req => req.trackId);

        const trackRateSetting = await this.prisma.setting.findFirst({
            where: {
                type: 'track-rate'
            },
        });

        if (!trackRateSetting) {
            throw new RpcException({
                code: Status.NOT_FOUND,
                message: 'Track rate setting not found'
            });
        }

        const price = parseFloat(trackRateSetting.option['rate']);

        const existingTrackRates = await this.prisma.trackRate.findMany({
            where: {
                track: {
                    key: {
                        in: trackIds
                    }
                },
                isDelete: false,
            },
            include: {
                track: true,
            },
        });

        if (existingTrackRates.length > 0) {
            throw new RpcException({
                code: Status.ALREADY_EXISTS,
                message: `Track rate has already been added for: ${existingTrackRates.map(rate => rate.track.key).join(', ')}`
            });
        }

        try {
            const results = await Promise.all(
                trackIds.map(trackId =>
                    this.elasticService.get({
                        index: 'gmi.tracks',
                        id: trackId,
                        _source: true,
                    }).catch(() => null)
                )
            );

            for (let i = 0; i < data.requests.length; i++) {
                const request = data.requests[i];
                const result = results[i];

                if (!result?._source) {
                    continue;
                }

                let track = await this.prisma.track.findFirst({
                    where: { isrc: result._source.isrc  },
                  });
          
                  const trackData = {
                    songTitle: result._source.title || null,
                    isrc: result._source.isrc || null,
                    credits: result._source.credits || null,
                    albumName: result._source.album?.name || null,
                    terms: result._source.terms || null,
                    upc: result._source.album?.upc || null,
                    originCountry: result._source.country || null,
                    label: result._source.album?.label || null,
                    updatedAt: new Date(),
                    singer: result._source.artist?.[0]?.name || null,
                    releaseDate: result._source.album?.releaseDate ? new Date(result._source.album?.releaseDate) : null,
                    duration: result._source.duration || null,
                    genre: result._source.genres?.length > 0 ? result._source.genres[0] : null,
                    key: request.trackId,
                  };
          
                  if (track) {
                    track = await this.prisma.track.update({
                      where: { id: track.id },
                      data: trackData,
                    });
                  } else {
                    track = await this.prisma.track.create({
                      data: trackData,
                    });
                  }
          
                  await this.prisma.trackRate.create({
                    data: {
                      trackId: track.id,
                      type: request.type,
                      price: price,
                    },
                  });
            }

            return {
                code: Status.OK,
                message: 'Successfully created track rates'
            };
        } catch (error) {
            throw new RpcException({
                code: Status.INTERNAL,
                message: 'Internal server error'
            });
        }
    }
}