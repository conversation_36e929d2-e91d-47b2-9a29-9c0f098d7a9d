import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { Status } from '@grpc/grpc-js/build/src/constants';
import { GrpcDeleteTrackRateCommand } from '../impl/grpc-delete-track-rate.command';

@CommandHandler(GrpcDeleteTrackRateCommand)
export class GrpcDeleteTrackRateHandler implements ICommandHandler<GrpcDeleteTrackRateCommand> {
    constructor(private readonly prisma: PrismaService) {}

    async execute(command: GrpcDeleteTrackRateCommand) {
        const { args } = command;

        const trackRates = await this.prisma.trackRate.findMany({
            where: {
              track: {
                key: {
                  in: args.ids
                }
              }
            },
            include: {
              track: true
            }
          });
      
          if (trackRates.length === 0) {
            throw new RpcException({
              code: Status.NOT_FOUND,
              message: 'Track rate not found'
            });
          }
      
          try {
            await this.prisma.trackRate.updateMany({
              where: {
                id: {
                  in: trackRates.map(rate => rate.id)
                }
              },
              data: {
                isDelete: true,
                deletedAt: new Date(),
              },
            });
      
            return {
              success: true,
              message: 'Track rate deleted successfully'
            };
          } catch (error) {
            throw new RpcException({
              code: Status.INTERNAL,
              message: error.message
            });
          }
        
    }
}