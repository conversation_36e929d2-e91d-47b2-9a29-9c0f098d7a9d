import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import {
  BadRequestException,
  ClassSerializerInterceptor,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';
import { ValidationError, useContainer } from 'class-validator';
import { ContextInterceptor, validateErrorFormat } from '@app/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { cwd } from 'process';
import { ReflectionService } from '@grpc/reflection';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: ['error', 'warn'],
  });

  const configService = app.get(ConfigService);

  app.useStaticAssets(join(__dirname, '..', 'public'));
  const grpcUrl = configService.get<string>('GRPC_URL');

  const grpcOptions: MicroserviceOptions = {
    transport: Transport.GRPC,
    options: {
      package: 'integration',
      protoPath: [
        join(cwd(), '_proto/common-proto/common.proto'),
        join(cwd(), '_proto/integration-proto/track-rate.proto'),
        join(cwd(), '_proto/integration-proto/event-category.proto'),
      ],
      url: grpcUrl,
      onLoadPackageDefinition: (pkg, server) => {
        new ReflectionService(pkg).addToServer(server);
      },
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
        includeDirs: [join(cwd(), '_proto')],
      },
    },
  };

  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  app.enableCors();

  app.enableVersioning({
    defaultVersion: '1',
    type: VersioningType.URI,
  });
  app.useGlobalInterceptors(new ContextInterceptor());
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: false,
      },
      whitelist: true,
      validateCustomDecorators: true,
      forbidUnknownValues: false,
      stopAtFirstError: true,
      exceptionFactory: (errors: ValidationError[]) => {
        const messages = validateErrorFormat(errors);
        return new BadRequestException(messages);
      },
    }),
  );

  if (configService.get('NODE_ENV') === 'development') {
    const config = new DocumentBuilder()
      .setTitle('SMR Integration API')
      .setDescription('The SMR Integration API')
      .setVersion('1.0')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api', app, document, {
      swaggerOptions: {
        tagsSorter: 'alpha',
        apisSorter: 'alpha',
        operationsSorter: 'alpha',
        docExpansion: 'none',
        persistAuthorization: true,
      },
    });
  }

  app.connectMicroservice(grpcOptions);
  await app.startAllMicroservices();
  await app.listen(configService.get<string>('APP_INTEGRATION_PORT'));
}
bootstrap();
