import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { PrismaService } from "nestjs-prisma";
import { CreateBankAccountCmoCommand } from "../impl/create-bank-account-cmo.command";
import { BadRequestException, Injectable } from "@nestjs/common";

@CommandHandler(CreateBankAccountCmoCommand)
@Injectable()
export class CreateBankAcccountCmoHandler implements ICommandHandler<CreateBankAccountCmoCommand> {
    constructor(private readonly prisma: PrismaService) { }

    async execute(command: CreateBankAccountCmoCommand) {
        const { args, user } = command;

        const bank = await this.prisma.bankIntegration.findUnique({
            where: { id: args.bankIntegrationId },
        });

        if (!bank) {
            throw new BadRequestException("Invalid bankId: Bank not found");
        }

        const existingBankAccountCmo = await this.prisma.bankAccountCmo.findFirst({
            where: {
                bankIntegrationId: args.bankIntegrationId,
                cmoType: args.cmoType,
                usage: args.usage
            },
        });

        if (existingBankAccountCmo) {
            throw new BadRequestException("Bank account already exists");
        }

        const data = await this.prisma.bankAccountCmo.create({
            data: {
                bankIntegrationId: args.bankIntegrationId,
                cmoType: args.cmoType,
                usage: args.usage,
                accountName: args.accountName,
                accountNumber: args.accountNumber,
                isActive: args.isActive ?? true,
                createdBy: user.id,
            },
        });

        return data;
    }
}