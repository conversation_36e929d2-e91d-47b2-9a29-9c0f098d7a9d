import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { PrismaService } from "nestjs-prisma";
import { UpdateBankAcccountCmoCommand } from "../impl";
import { Injectable, NotFoundException } from "@nestjs/common";

@CommandHandler(UpdateBankAcccountCmoCommand)
@Injectable()
export class UpdateBankAcccountCmoHandler implements ICommandHandler<UpdateBankAcccountCmoCommand> {
    constructor(private readonly prisma: PrismaService) { }

    async execute(command: UpdateBankAcccountCmoCommand) {
        const { args, id, user } = command;

        const existingBankAccount = await this.prisma.bankAccountCmo.findUnique({
            where: { id },
        });

        if (!existingBankAccount) {
            throw new NotFoundException('Bank account CMO not found');
        }

        if (args.bankIntegrationId) {
            const bank = await this.prisma.bankIntegration.findUnique({
                where: { id: args.bankIntegrationId },
            });

            if (!bank) {
                throw new NotFoundException('Bank not found');
            }
        }

        if (args.cmoType || args.usage) {
            const duplicate = await this.prisma.bankAccountCmo.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        { bankIntegrationId: args.bankIntegrationId || existingBankAccount.bankIntegrationId },
                        { cmoType: args.cmoType || existingBankAccount.cmoType },
                        { usage: args.usage || existingBankAccount.usage }
                    ]
                }
            });

            if (duplicate) {
                throw new NotFoundException('Bank account with same type and usage already exists');
            }
        }

        const data = await this.prisma.bankAccountCmo.update({
            where: { id },
            data: {
                ...args,
                updatedBy: user.id
            },
        });

        return data;
    }
}