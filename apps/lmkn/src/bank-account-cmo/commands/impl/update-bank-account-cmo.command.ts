import { ICommand } from "@nestjs/cqrs";
import { UpdateBankAccountCmoDto } from "../../dto/update-bank-account-cmo.dto";
import { ICurrentUser } from "apps/lmkn/src/auth/strategies/types/user.type";

export class UpdateBankAcccountCmoCommand implements ICommand {
    constructor(
        public readonly args: UpdateBankAccountCmoDto,
        public readonly id: string,
        public readonly user: ICurrentUser
    ) { }
}