import { BankAccountCmoTypeEnum, BankAccountCmoUsageEnum } from "@app/common/enums/bank-acccount-cmo.enum";
import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsOptional, IsBoolean, IsEnum } from "class-validator";

export class CreateBankAccountCmoDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    bankIntegrationId: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    accountName: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    accountNumber: string;

    @ApiProperty({ default: BankAccountCmoTypeEnum.LMKN, enum: BankAccountCmoTypeEnum })
    @IsEnum(BankAccountCmoTypeEnum)
    cmoType: BankAccountCmoTypeEnum;

    @ApiProperty({ default: BankAccountCmoUsageEnum.INBOUND, enum: BankAccountCmoUsageEnum })
    @IsEnum(BankAccountCmoUsageEnum)
    usage: BankAccountCmoUsageEnum;

    @ApiProperty({ default: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}