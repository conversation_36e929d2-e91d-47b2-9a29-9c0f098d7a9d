import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    UseGuards,
    Req,
    Ip,
} from '@nestjs/common';
import { CreateBankAccountCmoDto } from './dto/create-bank-account-cmo.dto';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
    CreateBankAccountCmoCommand,
    DeleteBankAccountCmoCommand,
    UpdateBankAcccountCmoCommand,
} from './commands';
import { GetBankAccountCmoQuery, GetBankAccountCmosQuery } from './queries';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { BaseFilterDto } from '@app/common';
import { UpdateBankAccountCmoDto } from './dto/update-bank-account-cmo.dto';

@ApiTags('Bank Account Cmo')
@Controller('bank-account-cmo')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class BankAccountCmoController {
    constructor(
        private queryBus: QueryBus,
        private commandBus: CommandBus,
    ) { }

    @Post()
    create(
        @Body() data: CreateBankAccountCmoDto,
        @User() user: ICurrentUser,
    ) {
        return this.commandBus.execute(new CreateBankAccountCmoCommand(data, user));
    }

    @Get()
    findAll(@Query() filter: BaseFilterDto) {
        return this.queryBus.execute(new GetBankAccountCmosQuery(filter));
    }

    @Get(':id')
    findOne(@Param('id') id: string) {
        return this.queryBus.execute(new GetBankAccountCmoQuery(id));
    }

    @Patch(':id')
    update(
        @Param('id') id: string,
        @Body() data: UpdateBankAccountCmoDto,
        @User() user: ICurrentUser,
    ) {
        return this.commandBus.execute(new UpdateBankAcccountCmoCommand(data, id, user));
    }

    @Delete(':id')
    remove(
        @Param('id') id: string,
    ) {
        return this.commandBus.execute(new DeleteBankAccountCmoCommand(id));
    }
}
