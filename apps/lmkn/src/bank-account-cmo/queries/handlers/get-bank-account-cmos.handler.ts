import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { BankAccountCmo, Prisma } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { GetBankAccountCmosQuery } from '../impl';

@QueryHandler(GetBankAccountCmosQuery)
export class GetBankAccountCmosHandler implements IQueryHandler<GetBankAccountCmosQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetBankAccountCmosQuery) {
    const { args } = query;
    const where: Prisma.BankAccountCmoWhereInput = {};

    if (args.search) {
      where.OR = [
        { accountName: { contains: args.search, mode: 'insensitive' } },
        { accountNumber: { contains: args.search, mode: 'insensitive' } },
      ];
    }

    const items = await Pagination<BankAccountCmo, Prisma.BankAccountCmoFindManyArgs>(
      this.prisma.bankAccountCmo,
      {
        where,
        include: {
          bankIntegration: true
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      { limit: args.limit, page: args.page },
    );

    return items;
  }
}
