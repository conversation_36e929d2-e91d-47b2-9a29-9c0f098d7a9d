import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetBankAccountCmoQuery } from '../impl';

@QueryHandler(GetBankAccountCmoQuery)
export class GetBankAccountCmoHandler implements IQueryHandler<GetBankAccountCmoQuery> {
  constructor(private prisma: PrismaService) { }

  async execute(query: GetBankAccountCmoQuery) {
    const { id } = query;
    const item = await this.prisma.bankAccountCmo.findFirst({ where: { id: id },   include: {
      bankIntegration:true
    } });
    if (!item) {
      throw new NotFoundException();
    }
    return item;
  }
}
