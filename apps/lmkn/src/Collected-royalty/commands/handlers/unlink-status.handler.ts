import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { UnlinkStatusCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { NotFoundException } from '@nestjs/common';
import { NotificationModelLmkn } from 'apps/lmkn/src/notification/models/notification.model';
import { ActivityLogModel } from 'apps/lmkn/src/activity-log/models/activity-log.model';
import { ActivityStatus, LmknAssetActionEnum } from '@app/common';

@CommandHandler(UnlinkStatusCommand)
export class UnlinkStatusHandler
  implements ICommandHandler<UnlinkStatusCommand>
{
  constructor(
    private prisma: PrismaService,
    private publisher: EventPublisher,
  ) {}

  async execute(command: UnlinkStatusCommand) {
    const { id, user, remark, clientIp, userAgent } = command;
    const item = await this.prisma.matching.findMany({
      where: { assetLibraryId: id, status: 'Linked' },
      include: {
        creator: {
          include: {
            lmk: { include: { lmkOnUser: { select: { userId: true } } } },
          },
        },
        assetLibrary: true
      },
    });

    if (!item) {
      throw new NotFoundException();
    }

    try {
      await this.prisma.matching.updateMany({
        where: { assetLibraryId: id, status: 'Linked' },
        data: { status: 'approved' },
      });

      const lmkItems = await this.prisma.lmkOnUser.findMany({
        where: {
          lmk: {
            creator: { some: { matching: { some: { assetLibraryId: id } } } },
          },
        },
      });

      item.map(async (matcingItm) => {
        await this.prisma.remarkMatching.create({
          data: {
            matchingId: matcingItm.id,
            remark: remark.remarks,
            reviewedId: matcingItm.creator.lmk.lmkOnUser[0].userId,
            reviewedLmknId: user.id,
            type: 'unlink-asset',
          },
        });
      });

      lmkItems.map(async (itms) => {
        await this.prisma.lmkNotification.createMany({
          data: {
            userId: itms.userId,
            type: 'unlink-asset',
            count: 1,
            description: 'Asset unlink',
          },
        });
      });

      const assetModel = this.publisher.mergeClassContext(ActivityLogModel);
      const assetvent = new assetModel();
      assetvent.createAssetLog(
        {
          userId: user.id,
          email: user.email,
          username: user.username,
          assetLibraryId: id,
          assetTitle: item[0].assetLibrary.songTitle,
          isrc: item[0].assetLibrary.isrc,
          status: ActivityStatus.SUCCESS,
          action: LmknAssetActionEnum.UNLINK,
          description: 'Asset successfully unlink',
          ipAddress: clientIp,
          userAgent: userAgent,
        }
      );

      return 'successfully unlink asset';
    } catch (error) {
      return 'failed unlink asset';
    }
  }
}
