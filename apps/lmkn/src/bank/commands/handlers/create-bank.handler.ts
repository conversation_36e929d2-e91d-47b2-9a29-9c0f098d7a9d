import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { CreateBankCommand } from '../impl';
import { HttpStatus } from '@nestjs/common';
import { ActivityLogModel } from 'apps/lmkn/src/activity-log/models/activity-log.model';
import { ActivityStatus, CudActionEnum, LmknMasterTypeEnum } from '@app/common';

@CommandHandler(CreateBankCommand)
export class CreateBankHandler implements ICommandHandler<CreateBankCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: CreateBankCommand) {
    const { args, user, clientIp, userAgent } = command;
    try {
      const lastBank = await this.prisma.bank.findFirst({
        orderBy: {
          code: 'desc'
        },
        where: {
          code: {
            startsWith: 'BID'
          }
        }
      });

      let nextSequence = 1;
      if (lastBank?.code) {
        const lastSequence = parseInt(lastBank.code.replace('BID', ''));
        nextSequence = lastSequence + 1;
      }

      const bankCode = `BID${nextSequence.toString().padStart(3, '0')}`;

      const item = await this.prisma.bank.create({
        data: {
          code: bankCode,
          name: args.name,
          description: args.description,
        },
      });

      const licenseModel = this.publisher.mergeClassContext(ActivityLogModel);
      const licenseEvent = new licenseModel();
      licenseEvent.createMasterLog(
        {
          userId: user.id,
          email: user.email,
          username: user.username,
          relationId: item.id,
          status: ActivityStatus.SUCCESS,
          type: LmknMasterTypeEnum.BANK,
          description: 'Bank successfully created',
          ipAddress: clientIp,
          userAgent: userAgent,
          action: CudActionEnum.CREATE
        }
      );
      return { status: HttpStatus.OK, msg: 'success created bank' };
    } catch (error) {
      return {
        status: HttpStatus.BAD_REQUEST,
        msg: 'failed created bank : ' + error,
      };
    }
  }
}
