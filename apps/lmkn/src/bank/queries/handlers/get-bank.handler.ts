import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetBankQuery } from '../impl';

@QueryHandler(GetBankQuery)
export class GetBankHandler implements IQueryHandler<GetBankQuery> {
  constructor(private prisma: PrismaService) { }

  async execute(query: GetBankQuery) {
    const { id } = query;
    const item = await this.prisma.bank.findFirst({
      where: { id: id }, 
    });
    if (!item) {
      throw new NotFoundException();
    }
    return item;
  }
}
