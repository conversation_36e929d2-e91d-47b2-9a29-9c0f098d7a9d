import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Bank, Prisma } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { GetBanksQuery } from '../impl';

@QueryHandler(GetBanksQuery)
export class GetBanksHandler implements IQueryHandler<GetBanksQuery> {
  constructor(private prisma: PrismaService) { }

  async execute(query: GetBanksQuery) {
    const { args } = query;
    const items = await Pagination<Bank, Prisma.BankFindManyArgs>(
      this.prisma.bank,
      {
        where: { name: { contains: args.search, mode: 'insensitive' }, }, 
      },
      { limit: args.limit, page: args.page },
    );
    return items;
  }
}
