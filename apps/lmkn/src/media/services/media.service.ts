import renameFile from '@app/common/helpers/file-manager/rename-file';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from 'minio';
import { PrismaService } from 'nestjs-prisma';
import { Readable } from 'stream';
import { Express } from 'express';

@Injectable()
export class MediaService {
  private readonly minioClient: Client;
  private readonly bucketName: string;
  private readonly lmknFolder: string;
  private readonly lmkFolder: string;
  private readonly creatorFolder: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.minioClient = this.createMinioClient();
    this.bucketName = this.configService.get(
      `${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`,
    );
    this.lmknFolder = this.configService.get('LMKN_MEDIA_FOLDER');
    this.lmkFolder = this.configService.get('LMK_MEDIA_FOLDER');
    this.creatorFolder = this.configService.get('CREATOR_MEDIA_FOLDER');
  }

  private createMinioClient(): Client {
    return new Client({
      endPoint: this.configService.get('MINIO_ENDPOINT'),
      port: parseInt(this.configService.get('MINIO_PORT') || '9000', 10),
      useSSL: this.configService.get('MINIO_USE_SSL') === 'true',
      accessKey: this.configService.get('MINIO_ACCESS_KEY'),
      secretKey: this.configService.get('MINIO_SECRET_KEY'),
    });
  }

  private createReadableStream(buffer: Buffer): Readable {
    const stream = new Readable({
      read() {
        this.push(buffer);
        this.push(null);
      },
    });

    stream.on('error', (error) => {
      stream.destroy();
    });

    return stream;
  }

  private getFileLocation(folder: string): string {
    if (this.configService.get('STORAGE_PROVIDER') !== 'minio') {
      return '';
    }

    const useHttps =
      this.configService.get('MINIO_USE_SSL') === 'true' ? 'https' : 'http';
    return `${useHttps}://${this.configService.get('MINIO_ENDPOINT')}:${this.configService.get('MINIO_PORT')}/${this.bucketName}/${folder}`;
  }

  private async handleFileUpload(
    file: Express.Multer.File,
    folder: string,
    mediaType: 'mediaLmkn' | 'mediaLmk' | 'mediaCreator',
  ) {
    let readableStream: Readable | null = null;

    try {
      const fileName = renameFile(file.originalname);
      const filePath = `${folder}/${fileName}`;

      readableStream = this.createReadableStream(file.buffer);

      await this.minioClient.putObject(
        this.bucketName,
        filePath,
        readableStream,
        file.size,
        { 'Content-Type': file.mimetype },
      );

      const mediaData = {
        fileName,
        path: `${this.bucketName}/${folder}`,
        name: file.originalname,
        encoding: file.encoding,
        mimeType: file.mimetype,
        size: file.size,
      };

      let media;
      switch (mediaType) {
        case 'mediaLmkn':
          media = await this.prisma.mediaLmkn.create({ data: mediaData });
          break;
        case 'mediaLmk':
          media = await this.prisma.mediaLmk.create({ data: mediaData });
          break;
        case 'mediaCreator':
          media = await this.prisma.mediaCreator.create({ data: mediaData });
          break;
      }

      return {
        ...media,
        fileLocation: this.getFileLocation(folder),
      };
    } catch (error) {
      throw new BadRequestException(`Failed to upload file: ${error.message}`);
    } finally {
      if (readableStream) {
        readableStream.destroy();
      }
    }
  }

  async uploadFileLmkn(file: Express.Multer.File) {
    const folder = this.lmknFolder;
    return this.handleFileUpload(file, folder, 'mediaLmkn');
  }

  async uploadFileLmk(file: Express.Multer.File) {
    const folder = this.lmkFolder;
    return this.handleFileUpload(file, folder, 'mediaLmk');
  }

  async uploadFileCreator(file: Express.Multer.File) {
    const folder = this.creatorFolder;
    return this.handleFileUpload(file, folder, 'mediaCreator');
  }

  private async handleFileDelete(
    mediaId: string,
    mediaType: 'mediaLmkn' | 'mediaLmk' | 'mediaCreator',
  ) {
    try {
      let media;
      switch (mediaType) {
        case 'mediaLmkn':
          media = await this.prisma.mediaLmkn.findUnique({
            where: { id: mediaId },
          });
          break;
        case 'mediaLmk':
          media = await this.prisma.mediaLmk.findUnique({
            where: { id: mediaId },
          });
          break;
        case 'mediaCreator':
          media = await this.prisma.mediaCreator.findUnique({
            where: { id: mediaId },
          });
          break;
      }

      if (!media) {
        throw new BadRequestException('Media not found');
      }

      await this.minioClient.removeObject(
        this.bucketName,
        `${media.path}/${media.fileName}`,
      );

      switch (mediaType) {
        case 'mediaLmkn':
          await this.prisma.mediaLmkn.delete({ where: { id: mediaId } });
          break;
        case 'mediaLmk':
          await this.prisma.mediaLmk.delete({ where: { id: mediaId } });
          break;
        case 'mediaCreator':
          await this.prisma.mediaCreator.delete({ where: { id: mediaId } });
          break;
      }

      return true;
    } catch (error) {
      throw new BadRequestException(`Failed to delete file: ${error.message}`);
    }
  }

  async deleteFileLmkn(mediaId: string) {
    return this.handleFileDelete(mediaId, 'mediaLmkn');
  }

  async deleteFileLmk(mediaId: string) {
    return this.handleFileDelete(mediaId, 'mediaLmk');
  }

  async deleteFileCreator(mediaId: string) {
    return this.handleFileDelete(mediaId, 'mediaCreator');
  }
}
