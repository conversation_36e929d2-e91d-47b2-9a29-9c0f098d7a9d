import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { PrismaService } from "nestjs-prisma";
import { UpdateBankIntegrationCommand } from "../impl";
import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";

@CommandHandler(UpdateBankIntegrationCommand)
@Injectable()
export class UpdateBankIntegrationHandler implements ICommandHandler<UpdateBankIntegrationCommand> {
    constructor(private readonly prisma: PrismaService) { }

    async execute(command: UpdateBankIntegrationCommand) {
        const { args, id, user } = command;

        const bankIntegration = await this.prisma.bankIntegration.findUnique({
            where: { id },
        });

        if (!bankIntegration) {
            throw new NotFoundException(`Bank integration not found`);
        }

        if (args.clientId && args.clientId !== bankIntegration.clientId) {
            const existingIntegration = await this.prisma.bankIntegration.findFirst({
                where: {
                    clientId: args.clientId,
                    id: { not: id },
                },
            });

            if (existingIntegration) {
                throw new BadRequestException("Client ID already exists for another bank integration");
            }
        }

        const updatedBankIntegration = await this.prisma.bankIntegration.update({
            where: { id },
            data: {
                bankName: args.bankName,
                clientId: args.clientId,
                clientSecret: args.clientSecret,
                webhookUrl: args.webhookUrl,
                isActive: args.isActive,
                quota: bankIntegration.quota,
                updatedBy: user.id,
                updatedAt: new Date(),
            },
        });

        return updatedBankIntegration;
    }
}