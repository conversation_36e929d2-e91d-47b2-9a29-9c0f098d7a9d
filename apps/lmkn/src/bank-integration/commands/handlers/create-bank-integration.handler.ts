import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { PrismaService } from "nestjs-prisma";
import { CreateBankIntegrationCommand } from "../impl/create-bank-integration.command";
import { BadRequestException, Injectable } from "@nestjs/common";
import { BankIntegrationStatusEnum } from "@app/common/enums/bank-integration.enum";

@CommandHandler(CreateBankIntegrationCommand)
@Injectable()
export class CreateBankIntegrationHandler implements ICommandHandler<CreateBankIntegrationCommand> {
    constructor(private readonly prisma: PrismaService) { }

    async execute(command: CreateBankIntegrationCommand) {
        const { args, user } = command;

        const lastBankId = await this.prisma.bankIntegration.findFirst({
            orderBy: {
                bankId: 'desc'
            },
            where: {
                bankId: {
                    startsWith: 'BID'
                }
            }
        });

        let nextSequence = 1;
        if (lastBankId?.bankId) {
            const lastSequence = parseInt(lastBankId?.bankId.replace('BID', ''));
            nextSequence = lastSequence + 1;
        }

        const bankId = `BID${nextSequence.toString().padStart(3, '0')}`;

        const existingIntegration = await this.prisma.bankIntegration.findFirst({
            where: {
                clientId: args.clientId,
            },
        });

        if (existingIntegration) {
            throw new BadRequestException("Bank integration already exists for this bank and client");
        }

        const bankIntegration = await this.prisma.bankIntegration.create({
            data: {
                bankName:args.bankName,
                bankId: bankId,
                clientId: args.clientId,
                clientSecret: args.clientSecret,
                webhookUrl: args.webhookUrl,
                status: BankIntegrationStatusEnum.NOT_CONNECTED,
                quota: 10,
                isActive: args.isActive ?? true,
                createdBy: user.id,
            },
        });

        return bankIntegration;
    }
}