import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Bank, Prisma } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { GetBankIntegrationsQuery } from '../impl';

@QueryHandler(GetBankIntegrationsQuery)
export class GetBankIntegrationsHandler implements IQueryHandler<GetBankIntegrationsQuery> {
  constructor(private prisma: PrismaService) { }

  async execute(query: GetBankIntegrationsQuery) {
    const { args } = query;
    const items = await Pagination<Bank, Prisma.BankIntegrationFindManyArgs>(
      this.prisma.bankIntegration,
      {
        where: args.search ? {
          OR: [
            {
              bankName: {
                  contains: args.search,
                  mode: 'insensitive',
                },
              },
          ]
        } : {},
        include: {
          BankAccountCmos: true
        }
      },
      { limit: args.limit, page: args.page },
    );
    return items;
  }
}
