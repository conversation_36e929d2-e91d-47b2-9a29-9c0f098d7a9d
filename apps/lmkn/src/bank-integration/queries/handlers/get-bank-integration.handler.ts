import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetBankIntegrationQuery } from '../impl';

@QueryHandler(GetBankIntegrationQuery)
export class GetBankIntegrationHandler implements IQueryHandler<GetBankIntegrationQuery> {
  constructor(private prisma: PrismaService) { }

  async execute(query: GetBankIntegrationQuery) {
    const { id } = query;
    const item = await this.prisma.bankIntegration.findFirst({ where: { id: id },  include: {
      BankAccountCmos: true
    } });
    if (!item) {
      throw new NotFoundException();
    }
    return item;
  }
}
