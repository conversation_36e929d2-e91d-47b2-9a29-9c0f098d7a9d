import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsOptional, IsBoolean, IsUrl } from "class-validator";


export class CreateBankIntegrationDto {
    @ApiProperty({ example: 'BCA BLUE' })
    @IsString()
    @IsNotEmpty()
    bankName: string;

    @ApiProperty({ example: 'ca_1GqIC8L7V1lM7z9kY3Q8x1nL9o2C8tKp' })
    @IsString()
    @IsNotEmpty()
    clientId: string;

    @ApiProperty({ example: '********************************' })
    @IsString()
    @IsNotEmpty()
    clientSecret: string;

    @ApiProperty({ example: 'https://yourdomain.com/webhook', required: false })
    @IsString()
    @IsOptional()
    @IsUrl()
    webhookUrl?: string;

    @ApiProperty({ example: true, required: false, default: true })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}