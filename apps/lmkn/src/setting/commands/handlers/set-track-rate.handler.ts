import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { SetTrackRateCommand } from '../impl';

@CommandHandler(SetTrackRateCommand)
export class SetTrackRateSettingHandler
  implements ICommandHandler<SetTrackRateCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: SetTrackRateCommand) {
    const { args } = command;
    try {
      return await this.prisma.$transaction(async (tx) => {
        await tx.setting.upsert({
          where: { type: 'track-rate' },
          create: {
            type: 'track-rate',
            option: { ...args },
          },
          update: {
            option: { ...args },
          },
        });

        await tx.trackRate.updateMany({
          where: {
            isDelete: false,
          },
          data: {
            price: args.rate,
          },
        });

        return 'successfully updated track rate and all existing rates';
      });
    } catch (error) {
      return 'failed to update track rate';
    }
  }
}
