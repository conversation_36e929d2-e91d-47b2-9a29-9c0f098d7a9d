import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PrismaModule, PrismaService } from 'nestjs-prisma';
import { join } from 'path';
import { AssetLibraryModule } from './asset-library/asset-library.module';
import { AuthModule } from './auth/auth.module';
import { BankModule } from './bank/bank.module';
import { CopyrightModule } from './copyright/copyright.module';
import { CreatorsModule } from './creators/creators.module';
import { DelegationModule } from './delegation/delegation.module';
import { DisputesModule } from './disputes/disputes.module';
import { GlobalModule } from './global/global.module';
import { JobPositionModule } from './job-position/job-position.module';
import { LicenseModule } from './license/license.module';
import { ListModule } from './list/list.module';
import { LogModule } from './log/logs.module';
import { PaymentModule } from './payment/payment.module';
import { PermissionModule } from './permission/permission.module';
import { PostalModule } from './postal/postal.module';
import { RegisterLmkModule } from './register-lmk/register-lmk.module';
import { ReportModule } from './report/report.module';
import { RoleModule } from './role-lmk/role.module';
import { RoyaltiesModule } from './royalties/royalties.module';
import { UnClaimModule } from './unclaim/unclaim.module';
import { UserModule } from './user/user.module';
import { ValidatorModule } from './validator/validator.module';
import { AvatarModule } from './avatar/avatar.module';
import { ClaimedAssetModule } from './claimed-asset/claimed-asset.module';
import { LegalizationPermitModule } from './legalization-permit/legalization-permit.module';
import { PermissionLmkModule } from './permission-lmk/permission-lmk.module';
import { RoleLmknModule } from './role-lmkn/role-lmkn.module';
import { NotifModule } from './notification/notif.module';
import { CountModule } from './count/count.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { DspIntegrationModule } from './dsp-integration/dsp-integration.module';
import { CollectedRoyaltyModule } from './Collected-royalty/collected-royalty.module';
import { RevenueProfileModule } from './revenue-profile/revenue-profile.module';
import { RevenueCalculationModule } from './revenue-calculation/revenue-calculation.module';
import { MoveLmkModule } from './moves-lmk/moves-lmk.module';
import { ScheduleModule } from '@nestjs/schedule';
import { PotentialRoyaltyModule } from './potential-royalty/potential-royalty.module';
import { CreateUnknowAccountModule } from './create-unknow-account/create-unknow-account.module';
import { ActivityLogModule } from './activity-log/activity-log.module';
import { AccessControlModule } from './access-control/access-control.module';
import { IPPoolModule } from './ip-pool/ip-pool.module';
import { AccessControlEnum, AccessControlMiddlewareFactory } from '@app/common';
import { MailFeatureModule } from './mail-feature/mail-feature.module';
import { SmtpConfigModule } from './smtp-config/smtp-config.module';
import { CategoryModule } from "./category/category.module"
import { cwd } from 'process';
import { CmoModule } from './cmo/cmo.module';
import { ContractModule } from './contract/contract.module';
import { HealthModule } from './health/health.module';
import { CustomersModule } from './customers/customers.module';
import { RoyaltyTemplateModule } from './royalty-template/royalty-template.module';
import { SearchModule } from './search/search.module';
import { EventModule } from './event/event.module';
import { TrackRateModule } from './track-rate/track-rate.module';
import { BankIntegrationModule } from './bank-integration/bank-integration.module';
import { BankAccountCmoModule } from './bank-account-cmo/bank-account-cmo.module';
import { SettingModule } from './setting/setting.module';
import { SongLibraryModule } from './song-library/song-library.module';
import { DigitalPlatformModule } from './digital-platform/digital-platform.module';
import { CmoMembershipModule } from './cmo-membership/cmo-membership.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    MailerModule.forRootAsync({
      useFactory: async (config: ConfigService, prismaService: PrismaService) => {
        const smtpConfig = await prismaService.smtpConfig.findFirst({
          where: {
            name: 'lmkn-smtp-config',
          }
        });
        return {
          transport: {
            host: smtpConfig?.mailHost || config.get<string>('MAIL_HOST'),
            port: smtpConfig?.mailPort || 465,
            secure: true,
            auth: {
              user: smtpConfig?.mailUser || config.get<string>('MAIL_USER'),
              pass: smtpConfig?.mailPass || config.get<string>('MAIL_PASSWORD'),
            },
          },
          defaults: {
            from: `"No Reply" <${smtpConfig?.mailFrom || config.get<string>('MAIL_FROM')}>`,
          },
          preview: false,
          template: {
            dir: join(cwd(), './mail/lmkn/templates'),
            adapter: new HandlebarsAdapter(),
            options: {
              strict: true,
            },
          },
        }
      },
      inject: [ConfigService, PrismaService],
    }),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    PrismaModule.forRoot({
      isGlobal: true,
      prismaServiceOptions: {
        prismaOptions: {
          log: [
            {
              emit: 'event',
              level: 'query',
            },
          ],
        },
      },
    }),
    AuthModule,
    ValidatorModule,
    BankModule,
    JobPositionModule,
    DelegationModule,
    RoleModule,
    RoleLmknModule,
    PostalModule,
    PermissionModule,
    PermissionLmkModule,
    UserModule,
    RegisterLmkModule,
    RoyaltiesModule,
    NotifModule,
    UnClaimModule,
    DisputesModule,
    DashboardModule,
    CountModule,
    PaymentModule,
    GlobalModule,
    CopyrightModule,
    CustomersModule,
    LicenseModule,
    CreatorsModule,
    ListModule,
    AssetLibraryModule,
    ReportModule,
    LogModule,
    AvatarModule,
    ClaimedAssetModule,
    LegalizationPermitModule,
    DspIntegrationModule,
    CollectedRoyaltyModule,
    RevenueProfileModule,
    RevenueCalculationModule,
    MoveLmkModule,
    PotentialRoyaltyModule,
    CreateUnknowAccountModule,
    ActivityLogModule,
    AccessControlModule,
    IPPoolModule,
    MailFeatureModule,
    SmtpConfigModule,
    // CategoryModule,
    CmoModule,
    ContractModule,
    HealthModule,
    RoyaltyTemplateModule,
    SearchModule,
    EventModule,
    TrackRateModule,
    BankIntegrationModule,
    BankAccountCmoModule,
    SettingModule,
    SongLibraryModule,
    DigitalPlatformModule,
    CmoMembershipModule
  ],
  providers: [PrismaService],
})
export class AppModule implements NestModule {
  constructor(private readonly prismaService: PrismaService) { }

  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(
        AccessControlMiddlewareFactory(this.prismaService, [AccessControlEnum.IP_ACCESS]),
      )
      .forRoutes('*');
  }
}