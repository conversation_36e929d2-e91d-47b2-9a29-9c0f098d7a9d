import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetResolutionsQuery } from '../impl';
import { Pagination } from '@app/common';
import { AssetLibrary, Prisma } from '@prisma/client';
import { HttpService } from '@nestjs/axios';
import { NotFoundException } from '@nestjs/common';
import { lastValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';

@QueryHandler(GetResolutionsQuery)
export class GetResolutionsHandler
  implements IQueryHandler<GetResolutionsQuery>
{
  constructor(
    private prisma: PrismaService,
    private httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async execute(query: GetResolutionsQuery) {
    const { args } = query;

    const items = await Pagination<
      AssetLibrary,
      Prisma.AssetLibraryFindManyArgs
    >(
      this.prisma.assetLibrary,
      {
        where: {
          AND: [
            {
              matching: {
                some: {
                  status: { in: ['resolve'] },
                },
              },
            },
            {
              songTitle: { contains: args.search, mode: 'insensitive' },
            },
          ],
        },
        include: {
          album: true,
        },
      },
      { limit: args.limit, page: args.page },
    );

    const assetId: string[] = [];
    items.data.map((itm) => {
      assetId.push(itm.id);
    });

    const matcingItems = await this.prisma.matching.findMany({
      where: {
        AND: [{ assetLibraryId: { in: assetId } }],
        NOT: {
          trackId: null,
        },
      },
      include: {
        track: {
          include: {
            unclaimAsset: {
              select: {
                playCount: true
              }
            }
          }
        },
        remarkMatching: true,
        creator: {
          include: {
            writer: {
              include: {
                creator: {
                  include: {
                    creatorOnUser: {
                      include: {
                        user: { include: { profileCreator: true } },
                      },
                    },
                  },
                },
              },
            },
            performer: true,
            producer: true,
            musician: true,
            publisher: true,
            lmk: true,
            creatorOnUser: {
              include: { user: { include: { profileCreator: true } } },
            },
          },
        },
        coCreator: {
          include: {
            creator: {
              include: {
                writer: {
                  include: {
                    creator: {
                      include: {
                        creatorOnUser: {
                          include: {
                            user: { include: { profileCreator: true } },
                          },
                        },
                      },
                    },
                  },
                },
                performer: true,
                producer: true,
                musician: true,
                publisher: true,
                lmk: true,
                creatorOnUser: {
                  include: {
                    user: { include: { profileCreator: true } },
                  },
                },
              },
            },
          },
        },  
      },
    });

    // const trackId: string[] = [];
    // matcingItems.map((itm) => {
    //   trackId.push(itm.gmiTrack.trackId);
    // });

    // if (trackId.length < 1) {
    //   throw new NotFoundException('matcing not yet');
    // }

    // const melodivaItems = await lastValueFrom(
    //   this.httpService.get(
    //     `http://${this.configService.get<string>('MELODIVA_BASE_URL')}/history-in?id=${trackId}`,
    //   ),
    // );

    // const dataMatching = matcingItems.map((itms) => {
    //   const mItems = melodivaItems.data.find(
    //     (mItm) => mItm.trackId == itms.gmiTrack.trackId,
    //   );
    //   return { ...itms, playcount: mItems };
    // });

    const data = items.data.map((itms) => {
      const matchingData = matcingItems.filter(
        (itm) => itm.assetLibraryId == itms.id,
      );

      return { ...itms, matching: matchingData };
    });

    // const modifiedData = data.map((item) => {
    //   const uniqueArtists = new Set();

    //   item.matching.forEach((match) => {
    //     if (match.playcount && match.playcount.artists) {
    //       match.playcount.artists.forEach((artist) =>
    //         uniqueArtists.add(artist),
    //       );
    //     }
    //   });

    //   return {
    //     ...item,
    //     artists: Array.from(uniqueArtists).map((artist) => ({ name: artist })),
    //   };
    // });

    return {
      data: JSON.parse(
        JSON.stringify(
          data.map((dt) => {
            dt.matching = dt.matching.map((dat) => {
              let playCount = 0;
              for (let i in dat.track.unclaimAsset) {
                playCount = playCount + dat.track?.unclaimAsset[i]?.playCount;
              }
              delete dat.track.unclaimAsset;
              dat['unclaimAsset'] = dat.track;
              delete dat.track;
              dat['unclaimAsset']['playCount'] = playCount;
              return dat;
            });
            return dt;
          }),
          (key, value) => (typeof value === 'bigint' ? Number(value) : value)
        )
      ),
      meta: items.meta,
    };
  }
}
