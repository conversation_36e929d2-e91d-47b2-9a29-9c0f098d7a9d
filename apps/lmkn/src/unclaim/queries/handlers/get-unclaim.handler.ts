import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetUnclaimQuery } from '../impl/get-unclaim.query';
import { PrismaService } from 'nestjs-prisma';
import { Pagination } from '@app/common';
import { Prisma } from '@prisma/client';
import { InternalServerErrorException } from '@nestjs/common';

@QueryHandler(GetUnclaimQuery)
export class GetUnclaimHandler implements IQueryHandler<GetUnclaimQuery> {
    constructor(private prisma: PrismaService) { }

    async execute(query: GetUnclaimQuery) {
        const { dspId, query: args } = query;
        const sort = args.sort || 'createdAt';
        const sortType = args.sortType || 'asc';

        try {
            const where: Prisma.UnclaimAssetPlayHistoryWhereInput = {
                OR: [
                    {
                        unclaimAsset: {
                            dspId,
                            ...(args.search ? {
                                OR: [
                                    { songTitle: { contains: args.search, mode: 'insensitive' } },
                                    { isrc: { contains: args.search, mode: 'insensitive' } },
                                    { singer: { contains: args.search, mode: 'insensitive' } },
                                    { album: { contains: args.search, mode: 'insensitive' } },
                                    { upc: { contains: args.search, mode: 'insensitive' } },
                                    { label: { contains: args.search, mode: 'insensitive' } },
                                ]
                            } : {}),
                        },
                    },
                    { deviceId: { contains: args.search, mode: 'insensitive' } },
                    { brandName: { contains: args.search, mode: 'insensitive' } },
                    { cid: { contains: args.search, mode: 'insensitive' } },
                    { ipAddress: { contains: args.search, mode: 'insensitive' } }
                ],
                ...(args.type ? {
                    type: decodeURIComponent(args.type)
                } : {})
            };

            const items = await Pagination<
                Prisma.UnclaimAssetPlayHistoryGetPayload<{
                    include: {
                        unclaimAsset: {
                            include: {
                                track: true
                            }
                        }
                    }
                }>, Prisma.UnclaimAssetPlayHistoryFindManyArgs>(
                    this.prisma.unclaimAssetPlayHistory,
                    {
                        where,
                        orderBy: { [sort]: sortType },
                        include: {
                            unclaimAsset: {
                                include: {
                                    track: true
                                }
                            }
                        }
                    },
                    {
                        limit: args.limit,
                        page: args.page,
                    }
                );

            const transformedData = items.data.map(playHistory => ({
                cid: playHistory.cid,
                playedAt: playHistory.createdAt,
                brandName: playHistory.brandName,
                industry: playHistory.industry,
                deviceId: playHistory.deviceId,
                deviceName: playHistory.deviceName,
                zoneName: playHistory.zoneName,
                os: playHistory.os,
                ipAddress: playHistory.ipAddress,
                origin: playHistory.origin,
                songTitle: playHistory.unclaimAsset.songTitle,
                singer: playHistory.unclaimAsset.singer,
                duration: playHistory.duration?.toString() || '',
                isrc: playHistory.unclaimAsset.isrc,
                album: playHistory.unclaimAsset.album,
                upc: playHistory.unclaimAsset.upc,
                label: playHistory.unclaimAsset.label,
                country: playHistory.unclaimAsset.country,
                type: playHistory.type,
                credits: playHistory.unclaimAsset.track?.credits ?? [],
            }));

            return {
                ...items,
                data: transformedData
            };

        } catch (error) {
            throw new InternalServerErrorException(
                'Failed to retrieve unclaim assets',
                error.message,
            );
        }
    }
}