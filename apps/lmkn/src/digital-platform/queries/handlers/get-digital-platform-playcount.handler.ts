import { <PERSON><PERSON>ueryHand<PERSON> } from "@nestjs/cqrs";
import { GetDigitalPlatformPlaycountQuery } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { Pagination } from "@app/common";
import { Prisma, UnclaimAsset } from "@prisma/client";

export class GetDigitalPlatformPlaycountHandler implements IQueryHandler<GetDigitalPlatformPlaycountQuery> {
    constructor(
        private readonly prisma: PrismaService,
    ) { }

    async execute(query: GetDigitalPlatformPlaycountQuery) {
        return 'ok'
    }
}