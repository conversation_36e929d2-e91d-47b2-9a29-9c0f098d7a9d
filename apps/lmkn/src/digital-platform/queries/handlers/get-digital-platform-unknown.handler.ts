import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetDigitalPlatformUnknownQuery } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { Pagination } from "@app/common";
import { Prisma, UnclaimAsset } from "@prisma/client";

export class GetDigitalPlatformUnknownHandler implements IQueryHandler<GetDigitalPlatformUnknownQuery> {
    constructor(
        private readonly prisma: PrismaService,
    ) { }

    async execute(query: GetDigitalPlatformUnknownQuery) {
        const { args } = query;
        const items = await Pagination<UnclaimAsset, Prisma.UnclaimAssetFindManyArgs>(
            this.prisma.unclaimAsset,
            {
                where: {
                    OR: [
                        {
                            dspId: args.dspId,
                            ...(args.search && {
                                trackId: {
                                    contains: args.search,
                                    mode: 'insensitive'
                                }
                            })
                        },
                    ]
                },
                include: {
                    playHistory: true
                }
            },
            { limit: args.limit, page: args.page },
        );
        return items;
    }
}