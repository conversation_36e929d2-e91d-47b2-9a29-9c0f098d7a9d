import { <PERSON><PERSON>ueryHand<PERSON> } from "@nestjs/cqrs";
import { GetDigitalPlatformRealtimeQuery } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { Pagination } from "@app/common";
import { Prisma, UnclaimAsset } from "@prisma/client";

export class GetDigitalPlatformRealtimeHandler implements IQueryHandler<GetDigitalPlatformRealtimeQuery> {
    constructor(
        private readonly prisma: PrismaService,
    ) { }

    async execute(query: GetDigitalPlatformRealtimeQuery) {
        return 'ok'
    }
}