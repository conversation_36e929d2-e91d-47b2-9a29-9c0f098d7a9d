import { <PERSON><PERSON>ueryHand<PERSON> } from "@nestjs/cqrs";
import { GetDigitalPlatformReportQuery } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { Pagination } from "@app/common";
import { Prisma, UnclaimAsset } from "@prisma/client";

export class GetDigitalPlatformReportHandler implements IQueryHandler<GetDigitalPlatformReportQuery> {
    constructor(
        private readonly prisma: PrismaService,
    ) { }

    async execute(query: GetDigitalPlatformReportQuery) {
        return 'ok'
    }
}