import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { SearchQuery } from '../impl';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(SearchQuery)
export class SearchHandler implements IQueryHandler<SearchQuery> {
  constructor(
    private readonly elastic: ElasticsearchService,
    private readonly config: ConfigService,
    private readonly prisma: PrismaService,
  ) {}

  async execute(query: SearchQuery) {
    const { args } = query;

    const limit = args.limit ? Number(args.limit) : 20;
    const curr = args.page && args.page > 0 ? Number(args.page) : 1;
    const from = (curr - 1) * limit;

    const keyword = (args.keyword ?? '').trim();
    const hasKeyLis = Array.isArray(args.key) && args.key.length > 0;

    if (!keyword && !hasKeyLis) {
      return {
        tracks: {
          meta: { total: 0, lastPage: 0, currentPage: 1, limit },
          data: [],
        },
        top: null,
      };
    }

    const esParams: any = {
      index: 'gmi.tracks',
      search_type: 'dfs_query_then_fetch',
      preference: 'primary',
      _source: {
        includes: [
          'id',
          'images',
          'title',
          'artists',
          'duration',
          'isrc',
          'terms',
          'album',
          'credits',
        ],
      },
      sort: [{ _score: 'desc' }],
    };

    if (keyword) {
      Object.assign(esParams, {
        query: {
          bool: {
            // must: [{ term: { isPublish: true } }],
            should: [
              { match: { terms: keyword } },
              { match: { terms: { query: keyword, fuzziness: 'AUTO' } } },
            ],
          },
        },
        from,
        size: limit,
      });
    } else {
      Object.assign(esParams, {
        query: {
          bool: {
            must: [
              // { term: { isPublish: true } },
              { terms: { _id: args.key } },
            ],
          },
        },
        from: 0,
        size: args.key.length,
      });
    }

    try {
      const hits = await this.elastic.search(esParams);
      const total = Number((hits.hits.total as any)?.value ?? 0);
      const last = Math.ceil(total / limit);

      const ids = hits.hits.hits.map((h) => (h._source as any).id);
      const db = await this.prisma.track.findMany({
        where: { key: { in: ids }, isDelete: false },
        select: { key: true, rate :{ select : { price: true } } },
      });
      const typeMap = new Map(
        db.map((t) => [
          t.key,
          {
            price: t.rate?.price || 0,
          },
        ]),
      );

      const topCmp = { type: null as any, data: null as any, score: 0 };

      const data = hits.hits.hits.map((h, i) => {
        const s: any = h._source;
        s.images = (s.images || []).map((img) => ({
          ...img,
          url: img.url
            ? `${this.config.get<string>('CDN_URL').replace(/\/$/, '')}${img.url}`
            : this.config.get<string>('DEFAULT_IMAGE_64_URL'),
        }));
        s.uri = `search:${s.id}:${keyword}`;
        const meta = typeMap.get(s.id);

        s.price = meta?.price ?? 0;

        if (keyword && i === 0 && h._score! > topCmp.score) {
          topCmp.type = 'song';
          topCmp.data = s;
          topCmp.score = h._score!;
        }
        return s;
      });

      return {
        tracks: {
          meta: { total, lastPage: last, currentPage: curr, limit },
          data,
        },
        top: keyword ? { type: topCmp.type, data: topCmp.data } : null,
      };
    } catch (err) {
      console.error(err);
      throw new InternalServerErrorException('Internal Server Error');
    }
  }
}
