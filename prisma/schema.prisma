// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "cockroachdb"
  url      = env("DATABASE_URL")
}

enum GenderType {
  male
  female
  unknown
}

enum TypePerformer {
  solo
  group
}

enum CategoryType {
  range
  ranges
  percentages
  percentage
  lupsum
  split
  progressives
}

model PermissionLmkn {
  id                   String                 @id @default(cuid())
  module               String                 @db.String(50)
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt()
  permissionOnRoleLmkn PermissionOnRoleLmkn[]
}

model PermissionOnRoleLmkn {
  role         RoleLmkn?       @relation(fields: [roleId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  roleId       String
  permission   PermissionLmkn? @relation(fields: [permissionId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  permissionId String
  manage       Boolean         @default(true)
  read         Boolean         @default(true)
  create       <PERSON>olean         @default(true)
  update       Boolean         @default(true)
  delete       Boolean         @default(true)

  @@unique([roleId, permissionId])
}

model RoleLmkn {
  id                   String                 @id @default(cuid())
  name                 String                 @db.String(50)
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt()
  permissionOnRoleLmkn PermissionOnRoleLmkn[]
  userLmkn             UserLmkn[]
}

model UserLmkn {
  id                String            @id @default(cuid())
  email             String            @db.String(100)
  username          String            @db.String(50)
  mobileNumber      String?           @db.String(15)
  password          String?           @db.String(200)
  activationCode    String?           @db.String(50)
  resetPasswordCode String?           @db.String(50)
  status            String            @db.String(30)
  provider          String            @default("local") @db.String(30)
  activationAt      DateTime?
  createdAt         DateTime?         @default(now())
  updatedAt         DateTime          @updatedAt()
  role              RoleLmkn?         @relation(fields: [roleId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  roleId            String?
  hashRt            String?           @db.String(150)
  profileLmkn       ProfileLmkn[]
  remarkMatching    RemarkMatching[]
  remarkMoveToLmk   RemarkMoveToLmk[]

  createdBankIntegrations BankIntegration[] @relation("CreatedByUser")
  updatedBankIntegrations BankIntegration[] @relation("UpdatedByUser")

  @@unique([email, username, mobileNumber])
  @@index([status, provider, hashRt])
}

model MediaLmkn {
  id          String        @id @default(cuid())
  path        String        @db.String(255)
  fileName    String        @unique
  name        String?       @db.String(255)
  encoding    String?       @db.String(50)
  mimeType    String?       @db.String(50)
  size        Int           @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt()
  profileLmkn ProfileLmkn[]
}

model ProfileLmkn {
  id           String      @id @default(cuid())
  firstName    String      @db.String(50)
  lastName     String?     @db.String(50)
  placeOfBirth String?     @db.String(20)
  dateOfBirth  DateTime?   @db.Date
  gender       GenderType? @default(unknown)
  address      String?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt()
  userLmkn     UserLmkn?   @relation(fields: [userId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  userId       String?
  media        MediaLmkn?  @relation(fields: [mediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  mediaId      String?

  @@unique([id])
}

model PermissionLmk {
  id                  String                @id @default(cuid())
  module              String                @db.String(50)
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt()
  permissionOnRoleLmk PermissionOnRoleLmk[]
}

model PermissionOnRoleLmk {
  role         RoleLmk?       @relation(fields: [roleId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  roleId       String
  permission   PermissionLmk? @relation(fields: [permissionId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  permissionId String
  manage       Boolean        @default(true)
  read         Boolean        @default(true)
  create       Boolean        @default(true)
  update       Boolean        @default(true)
  delete       Boolean        @default(true)

  @@unique([roleId, permissionId])
}

model RoleLmk {
  id                  String                @id @default(cuid())
  name                String                @db.String(50)
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt()
  permissionOnRoleLmk PermissionOnRoleLmk[]
  userLmk             UserLmk[]
}

model UserLmk {
  id                String            @id @default(cuid())
  email             String            @db.String(100)
  username          String            @db.String(50)
  mobileNumber      String?           @db.String(15)
  password          String?           @db.String(200)
  activationCode    String?           @db.String(50)
  resetPasswordCode String?           @db.String(50)
  status            String            @db.String(30)
  provider          String            @default("local") @db.String(30)
  activationAt      DateTime?
  createdAt         DateTime?         @default(now())
  updatedAt         DateTime          @updatedAt()
  roleLmk           RoleLmk?          @relation(fields: [roleId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  roleId            String?
  hashRt            String?           @db.String(150)
  lmkOnUser         LmkOnUser[]
  profileLmk        ProfileLmk[]
  remarkMatching    RemarkMatching[]
  payment           Payment[]
  remarkCreator     RemarkCreator[]
  lmkNotification   LmkNotification[]
  moveToLmk         MoveToLmk[]
  contracts         Contract[]
  claims            Claim[]

  @@unique([email, username, mobileNumber])
  @@index([status, provider, hashRt])
}

model MediaLmk {
  id         String       @id @default(cuid())
  path       String       @db.String(255)
  fileName   String       @unique
  name       String?      @db.String(255)
  encoding   String?      @db.String(50)
  mimeType   String?      @db.String(50)
  size       Int          @default(0)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt()
  profileLmk ProfileLmk[]
  contracts  Contract[]
}

model ProfileLmk {
  id           String      @id @default(cuid())
  firstName    String      @db.String(50)
  lastName     String?     @db.String(50)
  placeOfBirth String?     @db.String(20)
  dateOfBirth  DateTime?   @db.Date
  gender       GenderType? @default(unknown)
  address      String?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt()
  userLmk      UserLmk?    @relation(fields: [userId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  userId       String?
  media        MediaLmk?   @relation(fields: [mediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  mediaId      String?

  @@unique([id])
}

enum ContractStatus {
  APPROVED
  PENDING
  REJECTED
  EXPIRED
}

model Cmo {
  id          String     @id @default(cuid())
  society     String
  name        String
  country     String
  territories String[]
  contracts   Contract[]
  claims      Claim[]
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}

model Claim {
  id          String   @id @default(cuid())
  cmoId       String
  cmo         Cmo      @relation(fields: [cmoId], references: [id])
  track       Track?   @relation(fields: [trackId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  trackId     String?
  createdById String
  createdBy   UserLmk  @relation(fields: [createdById], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt()
}

model Contract {
  id               String             @id @default(cuid())
  userId           String
  user             UserLmk            @relation(fields: [userId], references: [id])
  cmoId            String
  cmo              Cmo                @relation(fields: [cmoId], references: [id])
  startDate        DateTime
  endDate          DateTime
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  description      String?
  status           ContractStatus     @default(PENDING)
  fileUrl          String?
  media            MediaLmk?          @relation(fields: [mediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  mediaId          String?
  LmknNotification LmknNotification[]
}

model Provinces {
  id     String   @id @default(cuid())
  name   String   @db.String(100)
  postal Postal[]

  @@index([name])
}

model Postal {
  id          String     @id @default(cuid())
  urban       String     @db.String(100)
  city        String     @db.String(100)
  district    String     @db.String(100)
  code        String     @db.String(10)
  provinces   Provinces? @relation(fields: [provincesId], references: [id])
  provincesId String?
  lmk         Lmk[]
  writer      Writer[]
  musician    Musician[]

  @@index([urban, city, district, code])
}

model Bank {
  id                     String                   @id @default(cuid())
  code                   String?
  name                   String                   @db.String(75)
  description            String?
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt()
  bankAccountCreator     BankAccountCreator[]
  bankAccountLmk         BankAccountLmk[]
  beneficiaryBankAccount BeneficiaryBankAccount[]

  @@unique([name])
}

model BankIntegration {
  id           String   @id @default(cuid())
  clientId     String
  clientSecret String
  webhookUrl   String?
  status       String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  createdBy    String?
  updatedAt    DateTime @updatedAt
  updatedBy    String?
  bankId       String
  bankName     String
  quota        Int   @default(0)

  createdUser UserLmkn? @relation("CreatedByUser", fields: [createdBy], references: [id], onDelete: SetNull)
  updatedUser UserLmkn? @relation("UpdatedByUser", fields: [updatedBy], references: [id], onDelete: SetNull)

  BankAccountCmos BankAccountCmo[]

  @@unique([bankId, bankName])
}

model BankAccountCmo {
  id            String   @id @default(cuid())
  accountName   String   @db.String(100)
  accountNumber String   @db.String(50)
  cmoType       String?
  usage         String?
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  createdBy     String?
  updatedAt     DateTime @updatedAt
  updatedBy     String?

  bankIntegrationId String
  bankIntegration   BankIntegration @relation(fields: [bankIntegrationId], references: [id], onDelete: Cascade)

  royaltyTemplates RoyaltyTemplate[]
}

model JobPosition {
  id          String          @id @default(uuid())
  name        String          @db.String(150)
  description String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt()
  persons     ContactPerson[]

  @@index([name])
}

model Delegation {
  id           BigInt            @id @default(autoincrement())
  name         String            @db.String(75)
  description  String?
  lmk          DelegationOnLmk[]
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt()
  creator      Creator[]
  children     Delegation[]      @relation("DelegationOnDelegation")
  parent       Delegation?       @relation("DelegationOnDelegation", fields: [parentId], references: [id])
  parentId     BigInt?
  creatorToken CreatorToken[]
  groupAdmin   GroupAdmin[]

  @@unique([name])
}

model Lmk {
  id               String             @id @default(uuid())
  organizationName String             @db.String(75)
  popularName      String?            @db.String(75)
  phoneNumber      String?            @db.String(15)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt()
  postal           Postal?            @relation(fields: [postalId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  postalId         String?
  legalization     Legalization?
  delegations      DelegationOnLmk[]
  creatorToken     CreatorToken[]
  lmkOnUser        LmkOnUser[]
  bankAccountLmk   BankAccountLmk[]
  creator          Creator[]
  lmkLoginActivity LmkLoginActivity[]
  contactPerson    ContactPerson[]
  groupAdmin       GroupAdmin[]

  @@unique([organizationName])
  @@index([popularName])
}

model LmkOnUser {
  id      String   @id @default(uuid())
  lmk     Lmk?     @relation(fields: [lmkId], references: [id])
  lmkId   String?
  userLmk UserLmk? @relation(fields: [userId], references: [id])
  userId  String?
}

model DelegationOnLmk {
  delegation   Delegation @relation(fields: [delegationId], references: [id])
  delegationId BigInt
  lmk          Lmk        @relation(fields: [lmkId], references: [id])
  lmkId        String

  @@id([delegationId, lmkId])
}

model Legalization {
  id             String              @id @default(uuid())
  legal          String?             @db.String(200)
  permit         String?             @db.String(200)
  corporateEmail String?             @db.String(100)
  npwp           String?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt()
  lmk            Lmk?                @relation(fields: [lmkId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  lmkId          String?             @unique
  file           LegalizationPermit?
}

model LegalizationPermit {
  id             String        @id @default(uuid())
  caption        String?
  path           String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt()
  legalization   Legalization? @relation(fields: [legalizationId], references: [id])
  legalizationId String        @unique
}

model BankAccountLmk {
  id          String   @id @default(uuid())
  number      String   @db.String(75)
  swift       String   @db.String(75)
  accountName String?  @db.String(100)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt()
  bank        Bank?    @relation(fields: [bankId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  bankId      String?
  lmk         Lmk?     @relation(fields: [lmkId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  lmkId       String?

  @@index([number, swift])
}

model LmkLoginActivity {
  id        String    @id @default(uuid())
  email     String?   @db.String(75)
  ipAddress String?   @db.String(20)
  device    String?   @db.String(15)
  location  String?   @db.String(30)
  browser   String?   @db.String(20)
  lmk       Lmk?      @relation(fields: [lmkId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  lmkId     String?
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt
}

model ContactPerson {
  id            String       @id @default(uuid())
  firstName     String?      @db.String(75)
  lastName      String?      @db.String(75)
  email         String       @db.String(100)
  phone         String
  createdAt     DateTime?    @default(now())
  updatedAt     DateTime?    @updatedAt
  lmk           Lmk?         @relation(fields: [lmkId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  lmkId         String?
  jobPosition   JobPosition? @relation(fields: [jobPositionId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  jobPositionId String?

  @@index([firstName, lastName, email, phone])
}

model PermissionCreator {
  id                      String                    @id @default(cuid())
  module                  String?                   @db.String(50)
  createdAt               DateTime?                 @default(now())
  updatedAt               DateTime?                 @updatedAt()
  permissionOnRoleCreator PermissionOnRoleCreator[]
}

model PermissionOnRoleCreator {
  role                RoleCreator?       @relation(fields: [roleId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  roleId              String
  permissionCreator   PermissionCreator? @relation(fields: [permissionCreatorId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  permissionCreatorId String
  manage              Boolean?           @default(true)
  read                Boolean?           @default(true)
  create              Boolean?           @default(true)
  update              Boolean?           @default(true)
  delete              Boolean?           @default(true)

  @@unique([roleId, permissionCreatorId])
}

model RoleCreator {
  id                      String                    @id @default(cuid())
  name                    String?                   @db.String(50)
  label                   String?                   @db.String(20)
  createdAt               DateTime?                 @default(now())
  updatedAt               DateTime?                 @updatedAt()
  permissionOnRoleCreator PermissionOnRoleCreator[]
  userCreator             UserCreator[]
}

model UserCreator {
  id                  String                @id @default(cuid())
  email               String                @db.String(100)
  username            String                @db.String(50)
  mobileNumber        String?               @db.String(15)
  mobileNumberCode    String?               @db.String(8)
  password            String?               @db.String(200)
  activationCode      String?               @db.String(50)
  resetPasswordCode   String?               @db.String(50)
  status              String                @db.String(30)
  provider            String                @default("local") @db.String(30)
  activationAt        DateTime?
  createdAt           DateTime?             @default(now())
  updatedAt           DateTime              @updatedAt()
  hashRt              String?               @db.String(150)
  isUnknow            Boolean?              @default(false)
  roleCreator         RoleCreator?          @relation(fields: [roleCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  roleCreatorId       String?
  creatorOnUser       CreatorOnUser[]
  profileCreator      ProfileCreator[]
  creatorNotification CreatorNotification[]
  potentialRoyalti    PotentialRoyalti[]
  groupAdmin          GroupAdmin[]
  groupAdminOnUser    GroupAdminOnUser[]

  @@unique([email, username, mobileNumber])
  @@index([status, provider, hashRt])
}

model ProfileCreator {
  id            String       @id @default(cuid())
  firstName     String       @db.String(80)
  lastName      String?      @db.String(80)
  placeOfBirth  String?      @db.String(20)
  dateOfBirth   DateTime?    @db.Date
  gender        GenderType?  @default(unknown)
  address       String?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt()
  userCreator   UserCreator? @relation(fields: [userCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  userCreatorId String?

  @@unique([id])
}

model GroupAdmin {
  id               String             @id @default(cuid())
  associationName  String?            @db.String(100)
  description      String?            @db.String(300)
  createdAt        DateTime?          @default(now())
  updatedAt        DateTime?          @updatedAt()
  lmk              Lmk?               @relation(fields: [lmkId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  lmkId            String?
  delegation       Delegation?        @relation(fields: [delegationId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  delegationId     BigInt?
  userCreator      UserCreator?       @relation(fields: [userCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  userCreatorId    String?
  groupHasMember   GroupHasMember[]
  groupAdminOnUser GroupAdminOnUser[]
}

model GroupAdminOnUser {
  id            String       @id @default(cuid())
  groupAdmin    GroupAdmin?  @relation(fields: [groupAdminId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  groupAdminId  String?
  userCreator   UserCreator? @relation(fields: [userCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  userCreatorId String?
  createdAt     DateTime?    @default(now())
  updatedAt     DateTime?    @updatedAt()

  @@unique([groupAdminId, userCreatorId])
}

model MediaCreator {
  id        String      @id @default(cuid())
  path      String      @db.String(255)
  fileName  String      @unique
  name      String?     @db.String(255)
  encoding  String?     @db.String(50)
  mimeType  String?     @db.String(50)
  size      Int         @default(0)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt()
  Writer    Writer[]
  producer  Producer[]
  publisher Publisher[]
  musician  Musician[]
  performer Performer[]
  coCreator CoCreator[]
}

model CreatorOnUser {
  id            String       @id @default(uuid())
  user          UserCreator? @relation(fields: [userCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  userCreatorId String?
  creator       Creator?     @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId     String?
}

model Creator {
  id                   String                 @id @default(uuid())
  token                String?
  isMain               Boolean?               @default(false)
  isActive             Boolean?               @default(true)
  status               String?                @db.String(20)
  createdAt            DateTime?              @default(now())
  updatedAt            DateTime?              @updatedAt
  delegation           Delegation?            @relation(fields: [delegationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  delegationId         BigInt?
  lmk                  Lmk?                   @relation(fields: [lmkId], references: [id])
  lmkId                String?
  creatorMedia         CreatorMedia?          @relation(fields: [creatorMediaId], references: [id])
  creatorMediaId       String?
  bankAccountOnCreator BankAccountOnCreator[]
  writer               Writer?
  creatorOnUser        CreatorOnUser[]
  producer             Producer[]
  publisher            Publisher[]
  musician             Musician[]
  performer            Performer[]
  remarkCreator        RemarkCreator[]
  assetLibraryActivity AssetLibraryActivity[]
  matching             Matching[]
  coCreator            CoCreator[]
  moveToLmk            MoveToLmk[]
}

model MoveToLmk {
  id              String            @id @default(uuid())
  lmkIdOld        String?
  lmkIdNew        String?
  status          String?           @db.String(20)
  creator         Creator?          @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId       String?
  reviewedLmk     UserLmk?          @relation(fields: [reviewedLmkId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  reviewedLmkId   String?
  remarkMoveToLmk RemarkMoveToLmk[]
}

model RemarkMoveToLmk {
  id            String     @id @default(uuid())
  remark        String?
  moveToLmk     MoveToLmk? @relation(fields: [moveToLmkId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  moveToLmkId   String?
  reviewedLmkn  UserLmkn?  @relation(fields: [reviwedLmknId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  reviwedLmknId String?
  createdAt     DateTime?  @default(now())
  updatedAt     DateTime?  @updatedAt()
}

model CreatorMedia {
  id                 String    @id @default(uuid())
  avatarMediaId      String?
  backgroundMendiaId String?
  createdAt          DateTime? @default(now())
  updatedAt          DateTime? @updatedAt
  creator            Creator[]
}

model RemarkCreator {
  id        String    @id @default(uuid())
  remark    String?
  creator   Creator?  @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId String?
  reviewed  UserLmk?  @relation(fields: [reviwedId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  reviwedId String?
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt
}

model BankAccountCreator {
  id                   String                 @id @default(uuid())
  accountName          String                 @db.String(50)
  accountNumber        String                 @db.String(75)
  swiftCode            String?                @db.String(75)
  main                 Boolean
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt()
  bank                 Bank?                  @relation(fields: [bankId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  bankId               String?
  bankAccountOnCreator BankAccountOnCreator[]

  @@index([accountNumber, swiftCode])
}

model BankAccountOnCreator {
  id                 String              @id @default(uuid())
  creator            Creator?            @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId          String?
  bankAccountCreator BankAccountCreator? @relation(fields: [bankAccountId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  bankAccountId      String?
}

model LegalCreator {
  id              String            @id @default(uuid())
  legalLaw        String
  permit          String            @db.String(200)
  nib             String            @db.String(100)
  businessLicense String            @db.String(85)
  npwp            String            @db.String(50)
  description     String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt()
  legalOnProducer LegalOnProducer[]
}

model ContactCreator {
  id                        String                      @id @default(uuid())
  fullName                  String                      @db.String(80)
  placeOfBirth              String?                     @db.String(80)
  dateOfBirth               DateTime?                   @db.Date
  jobPosition               String?                     @db.String(30)
  idCard                    String?                     @db.String(50)
  typeIdCard                String?                     @db.String(20)
  address                   String
  email                     String                      @db.String(100)
  mobileNumber              String                      @db.String(15)
  mobileNumberCode          String?                     @db.String(8)
  corporateEmail            String?                     @db.String(100)
  typeContact               String?                     @db.String(30)
  description               String?
  createdAt                 DateTime?                   @default(now())
  updatedAt                 DateTime?                   @updatedAt
  contactCreatorOnProducer  ContactCreatorOnProducer[]
  contactCreatorOnPublisher ContactCreatorOnPublisher[]
  matchingOnContactCreator  MatchingOnContactCreator[]
  coCreatorOnContactCreator CoCreatorOnContactCreator[]

  @@index([fullName])
}

model Beneficiary {
  id                     String                   @id @default(uuid())
  fullName               String                   @db.String(75)
  relation               String                   @db.String(30)
  placeOfBirth           String                   @db.String(20)
  dateOfBirth            DateTime                 @db.Date
  idNumber               String                   @db.String(50)
  typeIdCard             String?                  @db.String(20)
  npwp                   String                   @db.String(100)
  address                String
  email                  String                   @db.String(100)
  mobileNumber           String                   @db.String(15)
  mobileNumberCode       String?                  @db.String(8)
  jobPosition            String?                  @db.String(30)
  createdAt              DateTime?                @default(now())
  updatedAt              DateTime?                @updatedAt
  beneficiaryBankAccount BeneficiaryBankAccount[]
  beneficiaryOnWriter    BeneficiaryOnWriter[]
  beneficiaryOnPublisher BeneficiaryOnPublisher[]
  beneficiaryOnMusician  BeneficiaryOnMusician[]
  beneficiaryOnPerformer BeneficiaryOnPerformer[]
}

model BeneficiaryBankAccount {
  id            String       @id @default(uuid())
  accountName   String       @db.String(50)
  accountNumber String       @db.String(75)
  swiftCode     String       @db.String(75)
  main          Boolean
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt()
  bank          Bank?        @relation(fields: [bankId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  bankId        String?
  beneficiary   Beneficiary? @relation(fields: [beneficiaryId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  beneficiaryId String?

  @@index([accountNumber, swiftCode])
}

model Writer {
  id                  String                @id @default(cuid())
  popularName         String                @db.String(75)
  idNumber            String                @db.String(50)
  typeIdCard          String?               @db.String(20)
  npwp                String                @db.String(30)
  ipiNumber           String?
  workMobile          String?               @db.String(15)
  workMobileCode      String?               @db.String(8)
  dateOfDeath         DateTime?             @db.Date
  socialMedia         Json?                 @db.JsonB
  createdAt           DateTime?             @default(now())
  updatedAt           DateTime?             @updatedAt
  creator             Creator?              @relation(fields: [creatorId], references: [id])
  creatorId           String?               @unique
  postal              Postal?               @relation(fields: [postalId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  postalId            String?
  media               MediaCreator?         @relation(fields: [mediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  mediaId             String?
  beneficiaryOnWriter BeneficiaryOnWriter[]

  @@index([popularName])
}

model BeneficiaryOnWriter {
  id            String       @id @default(uuid())
  writer        Writer?      @relation(fields: [writerId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  writerId      String?
  beneficiary   Beneficiary? @relation(fields: [beneficiaryId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  beneficiaryId String?
}

model Producer {
  id                       String                     @id @default(cuid())
  companyName              String                     @db.String(80)
  companyAddress           String
  companyMobileNumber      String                     @db.String(15)
  companyMobileNumberCode  String?                    @db.String(8)
  companyEmail             String                     @db.String(100)
  companyWebsite           String
  socialMedia              Json?                      @db.JsonB
  ipi                      String?                    @db.String(30)
  createdAt                DateTime?                  @default(now())
  updatedAt                DateTime?                  @updatedAt
  creator                  Creator?                   @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId                String?
  media                    MediaCreator?              @relation(fields: [mediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  mediaId                  String?
  legalOnProducer          LegalOnProducer[]
  contactCreatorOnProducer ContactCreatorOnProducer[]
  groupHasMember           GroupHasMember[]

  @@index([companyName])
}

model GroupHasMember {
  id           String      @id @default(cuid())
  groupAdmin   GroupAdmin? @relation(fields: [groupAdminId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  groupAdminId String?
  producer     Producer?   @relation(fields: [producerId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  producerId   String?
  createdAt    DateTime?   @default(now())
  updatedAt    DateTime?   @updatedAt
}

model LegalOnProducer {
  id             String        @id @default(uuid())
  producer       Producer?     @relation(fields: [producerId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  producerId     String?
  legalCreator   LegalCreator? @relation(fields: [legalCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  legalCreatorId String?
}

model ContactCreatorOnProducer {
  id               String          @id @default(uuid())
  producer         Producer?       @relation(fields: [producerId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  producerId       String?
  contactCreator   ContactCreator? @relation(fields: [contactCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  contactCreatorId String?
}

model ContactCreatorOnPublisher {
  id               String          @id @default(uuid())
  publisher        Publisher?      @relation(fields: [publisherId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  publisherId      String?
  contactCreator   ContactCreator? @relation(fields: [contactCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  contactCreatorId String?
}

model Publisher {
  id                        String                      @id @default(cuid())
  companyName               String                      @db.String(75)
  companyAddress            String
  companyMobileNumber       String                      @db.String(15)
  companyMobileNumberCode   String?                     @db.String(8)
  companyEmail              String                      @db.String(100)
  companyWebsite            String
  socialMedia               Json?                       @db.JsonB
  ipi                       String?                     @db.String(30)
  createdAt                 DateTime?                   @default(now())
  updatedAt                 DateTime?                   @updatedAt
  creator                   Creator?                    @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId                 String?
  media                     MediaCreator?               @relation(fields: [mediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  mediaId                   String?
  beneficiaryOnPublisher    BeneficiaryOnPublisher[]
  contactCreatorOnPublisher ContactCreatorOnPublisher[]

  @@index([companyName])
}

model BeneficiaryOnPublisher {
  id            String       @id @default(uuid())
  publisher     Publisher?   @relation(fields: [publisherId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  publisherId   String?
  beneficiary   Beneficiary? @relation(fields: [beneficiaryId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  beneficiaryId String?
}

model Musician {
  id                    String                  @id @default(cuid())
  popularName           String                  @db.String(75)
  idNumber              String                  @db.String(50)
  typeIdNumber          String?                 @db.String(20)
  ipi                   String?                 @db.String(30)
  npwp                  String                  @db.String(30)
  socialMedia           Json?                   @db.JsonB
  workMobile            String                  @db.String(15)
  workMobileCode        String?                 @db.String(8)
  dateOfDeath           DateTime?               @db.Date
  createdAt             DateTime?               @default(now())
  updatedAt             DateTime?               @updatedAt
  postal                Postal?                 @relation(fields: [postalId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  postalId              String?
  creator               Creator?                @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId             String?
  media                 MediaCreator?           @relation(fields: [mediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  mediaId               String?
  creatorPosition       CreatorPosition?        @relation(fields: [creatorPositionId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorPositionId     String?
  performer             Performer?              @relation(fields: [performerId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  performerId           String?
  beneficiaryOnMusician BeneficiaryOnMusician[]

  @@index([popularName])
}

model BeneficiaryOnMusician {
  id            String       @id @default(uuid())
  musician      Musician?    @relation(fields: [musicianId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  musicianId    String?
  beneficiary   Beneficiary? @relation(fields: [beneficiaryId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  beneficiaryId String?
}

model CreatorPosition {
  id                    String                  @id @default(uuid())
  position              String?                 @db.String(25)
  description           String?                 @db.String(50)
  createdAt             DateTime?               @default(now())
  updatedAt             DateTime?               @updatedAt
  musician              Musician[]
  memberDetailPerformer MemberDetailPerformer[]
}

model Performer {
  id                     String                   @id @default(cuid())
  performerName          String                   @db.String(75)
  popularName            String                   @db.String(50)
  ipi                    String?                  @db.String(30)
  type                   TypePerformer            @default(solo)
  socialMedia            Json?                    @db.JsonB
  createdAt              DateTime?                @default(now())
  updatedAt              DateTime?                @updatedAt
  creator                Creator?                 @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId              String?
  media                  MediaCreator?            @relation(fields: [mediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  mediaId                String?
  beneficiaryOnPerformer BeneficiaryOnPerformer[]
  memberDetailPerformer  MemberDetailPerformer[]
  musician               Musician[]

  @@index([popularName])
}

model MemberDetailPerformer {
  id                   String           @id @default(uuid())
  fullName             String           @db.String(75)
  email                String           @db.String(100)
  mobileNumber         String           @db.String(15)
  mobileNumberCode     String?          @db.String(8)
  workMobileNumber     String?          @db.String(15)
  workMobileNumberCode String?          @db.String(8)
  placeOfBirth         String           @db.String(20)
  dateOfBirth          DateTime         @db.Date
  idCard               String           @db.String(50)
  typeIdCard           String?          @db.String(20)
  address              String?
  dateOfDeath          DateTime?        @db.Date
  createdAt            DateTime?        @default(now())
  updatedAt            DateTime?        @updatedAt
  performer            Performer?       @relation(fields: [performerId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  performerId          String?
  creatorPosition      CreatorPosition? @relation(fields: [creatorPositionId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorPositionId    String?
}

model BeneficiaryOnPerformer {
  id            String       @id @default(uuid())
  performer     Performer?   @relation(fields: [performerId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  performerId   String?
  beneficiary   Beneficiary? @relation(fields: [beneficiaryId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  beneficiaryId String?
}

model CreatorToken {
  id           String      @id @default(uuid())
  firstName    String?     @db.String(20)
  lastName     String?     @db.String(20)
  email        String?     @db.String(100)
  username     String?     @db.String(150)
  token        String?
  isUse        Boolean     @default(false)
  status       String?     @db.String(20)
  expireDate   DateTime?   @db.Date
  createdAt    DateTime?   @default(now())
  updatedAt    DateTime?   @updatedAt
  lmk          Lmk?        @relation(fields: [lmkId], references: [id])
  lmkId        String?
  delegation   Delegation? @relation(fields: [delegationId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  delegationId BigInt?

  @@index([firstName, lmkId])
}

model AssetLibrary {
  id                   String                 @id @default(uuid())
  songTitle            String                 @db.String(100)
  isrc                 String
  genre                String?                @db.String(50)
  duration             Int
  iswc                 String?
  originCountry        String                 @db.String(50)
  createdBy            String?                @db.String(30)
  bmiId                String?
  isSecond             Boolean                @default(false)
  isCount              String?                @db.String(3)
  createdAt            DateTime?              @default(now())
  updatedAt            DateTime?              @updatedAt
  assetLibraryActivity AssetLibraryActivity[]
  album                Album?                 @relation(fields: [albumId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  albumId              String?
  matching             Matching[]
  sourceLink           SourceLink[]
  lmknNotification     LmknNotification[]
}

model SourceLink {
  id             String        @id @default(uuid())
  dspName        String?       @db.String(30)
  link           String?
  createdAt      DateTime?     @default(now())
  updatedAt      DateTime?     @updatedAt
  assetLibrary   AssetLibrary? @relation(fields: [assetLibraryId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  assetLibraryId String?
}

model Album {
  id           String         @id @default(uuid())
  name         String?        @db.String(200)
  upc          String?        @db.String(70)
  releaseDate  DateTime?      @db.Date
  createdAt    DateTime?      @default(now())
  updatedAt    DateTime?      @updatedAt
  assetLibrary AssetLibrary[]

  @@unique([upc])
  @@index([upc, name])
}

model AssetLibraryActivity {
  id             String        @id @default(uuid())
  assetLibrary   AssetLibrary? @relation(fields: [assetLibraryId], references: [id])
  assetLibraryId String
  creator        Creator?      @relation(fields: [creatorId], references: [id])
  creatorId      String
  action         String?
  updatedBy      String?
  createdAt      DateTime?     @default(now())
  updatedAt      DateTime?     @updatedAt
}

model GmiTrack {
  id      String  @id @default(uuid())
  trackId String  @unique
  title   String?
}

model Matching {
  id                       String                     @id @default(uuid())
  status                   String?
  creatorType              String?
  portion                  String?
  claimedAt                DateTime?                  @db.Date
  linkedAt                 DateTime?                  @db.Date
  disputedAt               DateTime?                  @db.Date
  resolvedAt               DateTime?                  @db.Date
  createdAt                DateTime?                  @default(now())
  updatedAt                DateTime?                  @updatedAt
  creator                  Creator?                   @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId                String?
  assetLibrary             AssetLibrary?              @relation(fields: [assetLibraryId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  assetLibraryId           String?
  unclaimAsset             UnclaimAsset?              @relation(fields: [unclaimAssetId], references: [id])
  unclaimAssetId           String?
  remarkMatching           RemarkMatching[]
  coCreator                CoCreator[]
  matchingOnContactCreator MatchingOnContactCreator[]
  track                    Track?                     @relation(fields: [trackId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  trackId                  String?
}

model MatchingOnContactCreator {
  id               String          @id @default(uuid())
  contactCreator   ContactCreator? @relation(fields: [contactCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  contactCreatorId String?
  matching         Matching?       @relation(fields: [matchingId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  matchingId       String?
  createdAt        DateTime?       @default(now())
  updatedAt        DateTime?       @updatedAt
}

model CoCreator {
  id                        String                      @id @default(uuid())
  portion                   String?
  createdAt                 DateTime?                   @default(now())
  updatedAt                 DateTime?                   @updatedAt
  creator                   Creator?                    @relation(fields: [creatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  creatorId                 String?
  matching                  Matching?                   @relation(fields: [matchingId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  matchingId                String?
  contractStartDate         DateTime?                   @db.Date
  contractEndDate           DateTime?                   @db.Date
  media                     MediaCreator?               @relation(fields: [contractMediaId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  contractMediaId           String?
  contractDescription       String?
  coCreatorOnContactCreator CoCreatorOnContactCreator[]
}

model CoCreatorOnContactCreator {
  id               String          @id @default(uuid())
  contactCreator   ContactCreator? @relation(fields: [contactCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  contactCreatorId String?
  CoCreator        CoCreator?      @relation(fields: [coCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  coCreatorId      String?
  createdAt        DateTime?       @default(now())
  updatedAt        DateTime?       @updatedAt
}

model RemarkMatching {
  id             String    @id @default(uuid())
  remark         String?
  matching       Matching? @relation(fields: [matchingId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  matchingId     String?
  reviewed       UserLmk?  @relation(fields: [reviewedId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  reviewedId     String?
  reviewedLmkn   UserLmkn? @relation(fields: [reviewedLmknId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  reviewedLmknId String?
  type           String?   @db.String(20)
  createdAt      DateTime? @default(now())
  updatedAt      DateTime? @updatedAt()
}

model LmkNotification {
  id      String   @id @default(cuid())
  userId  String?  @db.String(60)
  userLmk UserLmk? @relation(fields: [userId], references: [id], onDelete: SetNull, onUpdate: SetNull)

  type        String?  @db.String(35)
  count       Int?
  description String?  @db.String(100)
  isActive    Boolean? @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([isActive, userId])
}

model LmknNotification {
  id             String        @id @default(cuid())
  type           String?       @db.String(35)
  count          Int?
  isActive       Boolean?      @default(true)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  assetLibrary   AssetLibrary? @relation(fields: [assetLibraryId], references: [id])
  assetLibraryId String?
  contract       Contract?     @relation(fields: [contractId], references: [id])
  contractId     String?

  @@index([isActive])
}

model CreatorNotification {
  id          String       @id @default(cuid())
  userId      String?      @db.String(60)
  userCreator UserCreator? @relation(fields: [userId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  type        String?      @db.String(35)
  count       Int?
  isActive    Boolean?     @default(true)
  description String?      @db.String(100)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  @@index([isActive, userId])
}

model LmknLoginActivity {
  id        String    @id @default(uuid())
  email     String?   @db.String(75)
  ipAddress String?   @db.String(20)
  device    String?   @db.String(15)
  location  String?   @db.String(30)
  browser   String?   @db.String(20)
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt
}

model License {
  id                  String              @id @default(uuid())
  code                String?
  dspId               String?
  dsp                 Dsp?                @relation(fields: [dspId], references: [id], onDelete: Cascade, onUpdate: SetNull)
  isUsed              Boolean             @default(false)
  status              String?             @db.String(15)
  createdAt           DateTime?           @default(now())
  updatedAt           DateTime?           @updatedAt
  licenseActivationId LicenseActivation[]
}

model Payment {
  id          String    @id @default(uuid())
  totalAmount String?
  status      String?
  userLmk     UserLmk?  @relation(fields: [userId], references: [id])
  userId      String?
  createdAt   DateTime? @default(now())
  updatedAt   DateTime? @updatedAt
}

model Dsp {
  id                  String                @id @default(uuid())
  dspName             String?               @db.String(25)
  clientId            String?               @unique
  clientSecret        String?
  hashRt              String?               @db.String(150)
  description         String?               @db.String(25)
  createdAt           DateTime?             @default(now())
  updatedAt           DateTime?             @updatedAt
  digital             Boolean?
  royaltyTemplate     RoyaltyTemplate?      @relation(fields: [royaltyTemplateId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  royaltyTemplateId   String?
  dspToken            DspToken[]
  revenueProfileOnDsp RevenueProfileOnDsp[]
  unclaimAsset        UnclaimAsset[]
  customer            Customer[]
  license             License[]
  isDsp               Boolean?              @default(true)
}

model DspToken {
  id           String    @id @default(uuid())
  accessToken  String?   @unique
  refreshToken String?   @unique
  expiresAt    DateTime?
  client       Dsp?      @relation(fields: [clientId], references: [clientId], onDelete: Cascade)
  clientId     String?

  @@index([clientId])
}

model RevenueProfile {
  id                  String                @id @default(uuid())
  rateName            String?               @db.String(50)
  typeCalculation     String?               @db.String(50)
  typeSources         String?               @db.String(50)
  createdAt           DateTime?             @default(now())
  updatedAt           DateTime?             @updatedAt
  revenueProfileOnDsp RevenueProfileOnDsp[]
  revenueCalculation  RevenueCalculation[]
}

model RevenueProfileOnDsp {
  id               String          @id @default(uuid())
  dsp              Dsp?            @relation(fields: [dspId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  dspId            String?
  revenueProfile   RevenueProfile? @relation(fields: [revenueProfileId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  revenueProfileId String?
  createdAt        DateTime?       @default(now())
  updatedAt        DateTime?       @updatedAt()
}

model RevenueCalculation {
  id               String          @id @default(uuid())
  amount           String?         @db.String(50)
  indexRate        String?         @db.String(50)
  period           String?         @db.String(20)
  cutOff           String?         @db.String(10)
  totalPlaycount   String?         @db.String(50)
  createdAt        DateTime?       @default(now())
  updatedAt        DateTime?       @updatedAt
  revenueProfile   RevenueProfile? @relation(fields: [revenueProfileId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  revenueProfileId String?
}

model UnclaimAsset {
  id               String                    @id @default(uuid())
  songTitle        String?                   @db.String(255)
  singer           String?                   @db.String(255)
  duration         Int?
  album            String?                   @db.String(255)
  upc              String?                   @db.String(50)
  label            String?                   @db.String(100)
  releaseDate      DateTime?                 @db.Date
  isrc             String?                   @db.String(50)
  playCount        Int?
  country          String?                   @db.String(3)
  createdAt        DateTime?                 @default(now())
  updatedAt        DateTime?                 @updatedAt
  dsp              Dsp?                      @relation(fields: [dspId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  dspId            String?
  matching         Matching[]
  track            Track?                    @relation(fields: [trackId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  trackId          String?
  unclaimAssetType UnlcaimAssetType[]
  playHistory      UnclaimAssetPlayHistory[]
}

model PotentialRoyalti {
  id            String       @id @default(uuid())
  tanggal       DateTime?    @default(now())
  revenue       String?      @db.String(20)
  currency      String?      @db.String(3)
  userCreator   UserCreator? @relation(fields: [userCreatorId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  userCreatorId String?
}

model Customer {
  id                  String             @id @default(uuid())
  sourceId            String?            @db.String(60)
  cid                 String?            @db.String(30)
  name                String?            @db.String(100)
  brand               String?            @db.String(100)
  industry            String?            @db.String(30)
  joinAt              DateTime?          @db.Timestamptz(3)
  status              String?            @db.String(30)
  isActive            Boolean?           @default(true)
  dspId               String?
  dsp                 Dsp?               @relation(fields: [dspId], references: [id], onDelete: Cascade, onUpdate: SetNull)
  createdAt           DateTime?          @default(now())
  updatedAt           DateTime?          @updatedAt
  licenseActivationId LicenseActivation?

  @@unique([sourceId])
}

model LicenseActivation {
  id          String    @id @default(uuid())
  licenseId   String?
  license     License?  @relation(fields: [licenseId], references: [id], onDelete: Cascade, onUpdate: SetNull)
  customerId  String?   @unique
  customer    Customer? @relation(fields: [customerId], references: [id], onDelete: Cascade, onUpdate: SetNull)
  activatedAt DateTime? @db.Timestamptz(3)
  expiredAt   DateTime? @db.Timestamptz(3)
  isActive    Boolean?  @default(true)
  createdAt   DateTime? @default(now())
  updatedAt   DateTime? @updatedAt
}

model LicenseLog {
  id                  Int       @id @default(sequence())
  licenseId           String?
  code                String?
  dspId               String?
  dspName             String?   @db.String(25)
  customerId          String?
  customerName        String?   @db.String(100)
  licenseActivationId String?
  activatedAt         DateTime? @db.Timestamptz(3)
  action              String?   @db.String(30)
  clientIp            String?   @db.String(20)
  createdAt           DateTime? @default(now())
}

model IntegrationRequestLog {
  id             Int       @id @default(sequence())
  method         String?   @db.String(10)
  path           String?   @db.String(100)
  statusCode     Int?
  dspId          String?
  dspName        String?   @db.String(25)
  queryParams    Json?     @db.JsonB
  body           Json?     @db.JsonB
  responseTimeMs Int?      @default(0)
  ip             String?   @db.String(45)
  userAgent      String?
  status         String?   @db.String(10)
  createdAt      DateTime? @default(now())

  @@index([dspName, ip, status, createdAt])
}

model CreatorAuthActivityLog {
  id          String    @id @default(uuid())
  email       String?   @db.String(75)
  description String?   @db.String(255)
  status      String?   @db.String(10)
  ipAddress   String?   @db.String(20)
  userAgent   String?
  userId      String?
  action      String?   @db.String(15)
  createdAt   DateTime? @default(now())

  @@index([action, createdAt])
}

model CreatorAdminAuthActivityLog {
  id          String    @id @default(uuid())
  userId      String?
  email       String?   @db.String(75)
  username    String?   @db.String(50)
  toUserId    String?
  toEmail     String?   @db.String(75)
  toUsername  String?   @db.String(50)
  description String?   @db.String(255)
  status      String?   @db.String(10)
  ipAddress   String?   @db.String(20)
  userAgent   String?
  action      String?   @db.String(15)
  createdAt   DateTime? @default(now())

  @@index([action, createdAt])
}

model CreatorMemberActivityLog {
  id             String    @id @default(uuid())
  userId         String?
  email          String?   @db.String(75)
  creatorId      String?
  creatorName    String?   @db.String(80)
  lmkId          String?
  lmkName        String?   @db.String(75)
  delegationId   BigInt?
  delegationName String?   @db.String(75)
  description    String?   @db.String(255)
  status         String?   @db.String(10)
  ipAddress      String?   @db.String(20)
  userAgent      String?
  action         String?   @db.String(15)
  createdAt      DateTime? @default(now())

  @@index([action, createdAt, userId])
}

model CreatorAssetActivityLog {
  id             Int       @id @default(sequence())
  creatorId      String?
  creatorName    String?   @db.String(80)
  lmkId          String?
  lmkName        String?   @db.String(75)
  delegationId   Int?
  delegationName String?   @db.String(75)
  assetLibraryId String?
  assetTitle     String    @db.String(100)
  isrc           String
  action         String?   @db.String(15)
  description    String?   @db.String(255)
  ipAddress      String?   @db.String(20)
  userAgent      String?
  status         String?   @db.String(10)
  createdAt      DateTime? @default(now())

  @@index([action, createdAt, isrc, creatorName, lmkName, delegationName, assetTitle])
}

model LmkAssetActivityLog {
  id             Int       @id @default(sequence())
  creatorId      String?
  creatorName    String?
  lmkId          String?
  lmkName        String?
  delegationId   String?
  delegationName String?
  assetLibraryId String?
  assetTitle     String
  isrc           String
  action         String?
  description    String?   @db.String(255)
  ipAddress      String?   @db.String(20)
  userAgent      String?
  status         String?   @db.String(10)
  createdAt      DateTime? @default(now())

  @@index([action, createdAt, isrc, creatorName, lmkName, delegationName, assetTitle])
}

model LmknUserManageActivityLog {
  id          Int       @id @default(sequence())
  userId      String?
  email       String?   @db.String(75)
  username    String?   @db.String(50)
  toUserId    String?
  toEmail     String?   @db.String(75)
  toUsername  String?   @db.String(50)
  description String?   @db.String(255)
  status      String?   @db.String(10)
  ipAddress   String?   @db.String(20)
  userAgent   String?
  action      String?   @db.String(15)
  createdAt   DateTime? @default(now())

  @@index([action, createdAt, userId, toUserId])
}

model LmknMasterActivityLog {
  id          Int       @id @default(sequence())
  userId      String?
  relationId  String?
  email       String?   @db.String(75)
  username    String?   @db.String(50)
  description String?   @db.String(255)
  status      String?   @db.String(10)
  type        String?   @db.String(15)
  ipAddress   String?   @db.String(20)
  userAgent   String?
  action      String?   @db.String(15)
  createdAt   DateTime? @default(now())

  @@index([action, createdAt, userId, email, username, status, type, ipAddress, relationId])
}

model LmknCmoActivityLog {
  id          Int       @id @default(sequence())
  userId      String?
  email       String?   @db.String(75)
  username    String?   @db.String(50)
  cmoId       String?
  cmoName     String?   @db.String(75)
  description String?   @db.String(255)
  status      String?   @db.String(10)
  ipAddress   String?   @db.String(20)
  userAgent   String?
  action      String?   @db.String(15)
  createdAt   DateTime? @default(now())

  @@index([action, createdAt, userId, cmoName, ipAddress, status])
}

model LmknTransferActivityLog {
  id          Int       @id @default(sequence())
  userId      String?
  email       String?   @db.String(75)
  username    String?   @db.String(50)
  creatorId   String?
  creatorName String?   @db.String(75)
  lmkFromId   String?
  lmkFromName String?   @db.String(75)
  lmkToId     String?
  lmkToName   String?   @db.String(75)
  description String?   @db.String(255)
  status      String?   @db.String(10)
  ipAddress   String?   @db.String(20)
  userAgent   String?
  action      String?   @db.String(15)
  createdAt   DateTime? @default(now())

  @@index([action, createdAt, userId])
}

model LmknAssetActivityLog {
  id             Int       @id @default(sequence())
  userId         String?
  email          String?   @db.String(75)
  username       String?   @db.String(50)
  assetLibraryId String?
  assetTitle     String    @db.String(100)
  isrc           String
  action         String?   @db.String(15)
  description    String?   @db.String(255)
  ipAddress      String?   @db.String(20)
  userAgent      String?
  status         String?   @db.String(10)
  createdAt      DateTime? @default(now())

  @@index([action, createdAt, isrc, assetTitle, email, username, ipAddress, status])
}

model AccessControl {
  id                    Int                     @id @default(sequence())
  name                  String?                 @db.String(25)
  description           String?                 @db.String(255)
  expireDate            DateTime?               @db.Date
  options               Json?                   @db.JsonB
  isEnable              Boolean                 @default(true)
  createdAt             DateTime?               @default(now())
  updatedAt             DateTime?               @updatedAt
  accessControlOnIpPool AccessControlOnIpPool[]

  @@unique([name])
}

model AccessControlOnIpPool {
  id              String         @id @default(cuid())
  accessControl   AccessControl? @relation(fields: [accessControlId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  accessControlId Int?
  ipPool          IpPool?        @relation(fields: [ipPoolId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  ipPoolId        String?
  name            String?        @db.String(60)

  @@unique([accessControlId, ipPoolId])
}

model IpPool {
  id                    String                  @id @default(uuid())
  ip                    String?                 @db.String(25)
  version               Int?                    @default(4)
  createdAt             DateTime?               @default(now())
  updatedAt             DateTime?               @updatedAt
  accessControlOnIpPool AccessControlOnIpPool[]

  @@index([ip])
}

model SmtpConfig {
  id                      String                    @id @default(cuid())
  name                    String?                   @db.String(25)
  tag                     String?                   @db.String(12)
  description             String?                   @db.String(255)
  mailHost                String?                   @db.String(30)
  mailUser                String?                   @db.String(60)
  mailPass                String?                   @db.String(255)
  mailFrom                String?                   @db.String(60)
  mailPort                Int?
  createdAt               DateTime?                 @default(now())
  updatedAt               DateTime?                 @updatedAt
  smtpConfigOnMailFeature SmtpConfigOnMailFeature[]

  @@unique([tag])
}

model MailFeature {
  id                      Int                       @id @default(sequence())
  name                    String?                   @db.String(25)
  requiredFields          Json?                     @db.JsonB
  smtpConfigOnMailFeature SmtpConfigOnMailFeature[]

  @@unique([name])
}

model SmtpConfigOnMailFeature {
  id            String       @id @default(cuid())
  smtpConfig    SmtpConfig?  @relation(fields: [smtpConfigId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  smtpConfigId  String?
  mailFeature   MailFeature? @relation(fields: [mailFeatureId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  mailFeatureId Int?
  subject       String?      @db.String(255)
  isEnable      Boolean      @default(true)
  fileName      String?      @unique
  templatePath  String?      @db.String(255)

  @@unique([smtpConfigId, mailFeatureId])
}

model Track {
  id            String         @id @default(cuid())
  key           String?
  songTitle     String?        @db.String(255)
  singer        String?        @db.String(255)
  duration      Int?
  albumName     String?        @db.String(255)
  upc           String?        @db.String(50)
  label         String?        @db.String(100)
  releaseDate   DateTime?      @db.Date
  isrc          String?        @unique @db.String(50)
  dspIds        String[]
  genre         String?        @db.String(50)
  iswc          String?
  originCountry String?        @db.String(50)
  unclaimAsset  UnclaimAsset[]
  credits       Json?          @db.JsonB
  matching      Matching[]
  claims        Claim[]
  createdAt     DateTime?      @default(now())
  updatedAt     DateTime?      @updatedAt
  EventSong     EventSong[]
  rate          TrackRate?
  isDelete      Boolean        @default(false)
  terms         String?
}

model TrackRate {
  id        String    @id @default(cuid())
  track     Track     @relation(fields: [trackId], references: [id], onDelete: Cascade)
  trackId   String    @unique
  price     BigInt?
  type      String?
  isActive  Boolean?  @default(true)
  isDelete  Boolean   @default(false)
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model RoyaltyTemplate {
  id               String                           @id @default(cuid())
  code             String
  name             String
  bankAccountCmoId String?
  bankAccountCmo   BankAccountCmo?                  @relation(fields: [bankAccountCmoId], onDelete: SetNull, references: [id])
  percentage       Float
  groupId          String?
  group            RoyaltyTemplateGroup?            @relation(fields: [groupId], references: [id])
  dsps             Dsp[]
  rightCategories  RoyaltyTemplateOnRightCategory[]
  createdAt        DateTime                         @default(now())
  updatedAt        DateTime                         @updatedAt
}

model RoyaltyRightCategory {
  id          String                           @id @default(cuid())
  name        String
  description String?
  items       RoyaltyTemplateOnRightCategory[]
  createdAt   DateTime                         @default(now())
  updatedAt   DateTime                         @updatedAt
}

model RoyaltyTemplateOnRightCategory {
  id              String               @id @default(cuid())
  royaltyTemplate RoyaltyTemplate      @relation(fields: [templateId], references: [id], onDelete: Cascade)
  templateId      String
  rightCategory   RoyaltyRightCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  categoryId      String
  createdAt       DateTime             @default(now())
  updatedAt       DateTime             @updatedAt

  @@unique([templateId, categoryId])
}

model RoyaltyTemplateGroup {
  id          String            @id @default(cuid())
  code        String
  name        String
  description String?
  items       RoyaltyTemplate[]
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
}

model Event {
  id             String   @id @default(cuid())
  name           String
  type           String
  promoter       String
  organizer      String
  startDate      DateTime
  endDate        DateTime
  durationDays   Int
  mainVenue      String
  subVenue       String
  basedOn        String
  complimentary  Float
  commercial     Float
  royaltyRevenue Float

  category   EventCategory? @relation(fields: [categoryId], references: [id])
  categoryId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  EventSong     EventSong[]
  EventRevenues EventRevenue[]
  license       EventLicense?
}

model EventCategory {
  id          String   @id @default(cuid())
  name        String?
  description String?
  active      Boolean?
  code        String?
  isActive    Boolean? @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt
  createdBy   String?
  updatedBy   String?

  events            Event[]
  eventCategoryType EventCategoryType[]

  @@index([name])
}

model EventCategoryType {
  id          String         @id @default(cuid())
  name        String?
  description String?
  isActive    Boolean?       @default(true)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @default(now()) @updatedAt
  category    EventCategory? @relation(fields: [categoryId], references: [id])
  categoryId  String?

  @@index([name])
}

model EventSong {
  id        String   @id @default(cuid())
  event     Event?   @relation(fields: [eventId], references: [id])
  eventId   String?
  track     Track?   @relation(fields: [trackId], references: [id])
  trackId   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model EventLicense {
  id                String    @id @default(cuid())
  event             Event     @relation(fields: [eventId], references: [id])
  eventId           String    @unique
  number            String?
  certificateNumber String?
  releaseDate       DateTime?
  approvedBy        String?
  approvedAt        DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  expiredAt         DateTime?
  status            String?
}

model EventRevenue {
  id               String   @id @default(cuid())
  event            Event    @relation(fields: [eventId], references: [id])
  eventId          String
  ticketAgent      String?
  ticketCategory   String
  ticketPrice      Float
  complimentaryQty Int
  complimentaryRev Float
  commercialQty    Int
  commercialRev    Float
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

model Category {
  id           String                @id @default(uuid())
  code         String
  name         String
  type         CategoryType?
  createdAt    DateTime?             @default(now())
  updatedAt    DateTime?             @updatedAt
  rangeLabel   String?
  unitLabel    String?
  ranges       CategoryRange[]
  percentages  CategoryPercentage[]
  lupsum       CategoryLupsum?       @relation(fields: [lupsumId], references: [id])
  lupsumId     String?
  split        CategorySplit?        @relation(fields: [splitId], references: [id])
  splitId      String?
  progressives CategoryProgressive[]
  children     Category[]            @relation("CategoryToCategory")
  parent       Category?             @relation("CategoryToCategory", fields: [parentId], references: [id])
  parentId     String?

  @@unique([code])
}

model CategoryProgressive {
  id         String   @id @default(uuid())
  categoryId String
  category   Category @relation(fields: [categoryId], references: [id])
  min        Int
  max        Int?
  creatorFee Int
  relatedFee Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model CategoryRange {
  id         String   @id @default(uuid())
  min        Float
  max        Float
  cost       Float
  category   Category @relation(fields: [categoryId], references: [id])
  categoryId String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model CategoryPercentage {
  id         String   @id @default(uuid())
  name       String
  percentage Decimal  @db.Decimal(5, 2)
  category   Category @relation(fields: [categoryId], references: [id])
  categoryId String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model CategoryLupsum {
  id         String     @id @default(uuid())
  name       String
  rate       Float
  categories Category[]
  createdAt  DateTime   @default(now())
  categoryId String?
  updatedAt  DateTime   @updatedAt
}

model CategorySplit {
  id         String     @id @default(uuid())
  name       String
  percentage Decimal    @db.Decimal(5, 2)
  split      Decimal    @db.Decimal(5, 2)
  categories Category[]
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
}

model UnlcaimAssetType {
  id             String        @id @default(cuid())
  type           String?       @db.String(50)
  unclaimAssetId String?
  unclaimAsset   UnclaimAsset? @relation(fields: [unclaimAssetId], references: [id], onDelete: SetNull, onUpdate: SetNull)
}

model UnclaimAssetPlayHistory {
  id             String        @id @default(cuid())
  cid            String?
  brandName      String?
  deviceId       String?
  type           String
  duration       Int?
  deviceName     String?
  zoneName       String?
  origin         String?
  industry       String?
  os             String?
  ipAddress      String?
  propertyId     String?
  createdAt      DateTime?     @default(now())
  updatedAt      DateTime?     @updatedAt
  unclaimAsset   UnclaimAsset? @relation(fields: [unclaimAssetId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  unclaimAssetId String?

  @@index([cid, deviceId, ipAddress])
}

model Setting {
  type   String @id
  option Json   @db.JsonB
}
