version: '3.9'
services:
  lmkn:
    container_name: api-lmkn-new
    image: gitlab.vnt.co.id:5050/smr-1/backend-3-service-smr/lmkn-dev:latest
    restart: unless-stopped
    environment:
      - APP_LMK_SERVER=https://api.lmk-dev.royalti.co.id
      - APP_LMKN_SERVER=https://api.lmkn-dev.royalti.co.id/
      - APP_LMKN_PORT=8005
      - NODE_ENV=development
      - DATABASE_URL=*************************************************/lmkn_dev
      - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - GOOGLE_CLIENT_ID=1011196471658-irtrgsaf977o3fialroa83g1lv4vfthg.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - CALLBACK_URL=https://api-stg04.melodiva.co.id/v1/auth/google/callback
      - MAIL_HOST=inmail.vnt.net.id
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
      - MAIL_FROM=<EMAIL>
      - GMI_SVC='*************:50001'
      - LMK_BASE_URL=https://api.lmk-dev.royalti.co.id
      - LMKN_BASE_URL=https://api.lmkn-dev.royalti.co.id
      - RMS_CLIENT_ID=d9ZXPBdiMXweztOo9ffN
      - CDN_URL=https://cdn.velodiva.com/
      - ELASTIC_NODE=http://*************:9200
      - MINIO_ENDPOINT=bucket.velodiva.com
      - MINIO_PORT=443
      - MINIO_USE_SSL=true
      - MINIO_ACCESS_KEY=sw0aTB827hiw36xFkY7E
      - MINIO_SECRET_KEY=xssOVt9jupA35Ygluja82KkKmO3bZdC2yPEjysh3
      - MINIO_BUCKET_NAME=smr
      - STORAGE_PROVIDER=minio
      - LMKN_MEDIA_FOLDER=lmkn-media
      - LMK_MEDIA_FOLDER=lmk-media
      - CREATOR_MEDIA_FOLDER=creator-media
    ports:
      - '8005:8005'
    volumes:
      - melodiva:/app/storage
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  lmk:
      container_name: api-lmk-new
      image: gitlab.vnt.co.id:5050/smr-1/backend-3-service-smr/lmk-dev:latest
      restart: unless-stopped
      environment:
        - NODE_ENV=development
        - APP_LMK_SERVER=https://api.lmk-dev.royalti.co.id
        - APP_LMK_PORT=8006
        - DATABASE_URL=*************************************************/lmkn_dev
        - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
        - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
        - GOOGLE_CLIENT_ID=1011196471658-irtrgsaf977o3fialroa83g1lv4vfthg.apps.googleusercontent.com
        - GOOGLE_CLIENT_SECRET=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
        - CALLBACK_URL=https://api-stg02.melodiva.co.id/v1/auth/google/callback
        - MAIL_HOST=inmail.vnt.net.id
        - MAIL_USER=<EMAIL>
        - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
        - MAIL_FROM=<EMAIL>
        - GMI_SVC='*************:50001'
        - LMK_BASE_URL=https://api.lmk-dev.royalti.co.id
        - LMKN_BASE_URL=https://api.lmkn-dev.royalti.co.id
        - RMS_CLIENT_ID=d9ZXPBdiMXweztOo9ffN
        - CDN_URL=https://cdn.velodiva.com/
        - ELASTIC_NODE=http://*************:9200
      ports:
        - '8006:8006'
      volumes:
        - melodiva:/app/storage
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  integration:
      container_name: api-integration
      image: gitlab.vnt.co.id:5050/smr-1/backend-3-service-smr/integration-dev:latest
      restart: unless-stopped
      environment:
        - NODE_ENV=development
        - APP_INTEGRATION_SERVER=0.0.0.0
        - APP_INTEGRATION_PORT=8010
        - DATABASE_URL=*************************************************/lmkn_dev?schema=public
        - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
        - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
        - RMS_CLIENT_ID=d9ZXPBdiMXweztOo9ffN
        - GRPC_URL=0.0.0.0:50051
        - CDN_URL=https://cdn.velodiva.com/
        - ELASTIC_NODE=http://*************:9200
      ports:
        - '8010:8010'
        - '50051:50051'
      volumes:
        - melodiva:/app/storage

volumes:
  melodiva:
    driver: local
    driver_opts:
      type: 'none'
      o: 'bind'
      device: '/srv/storage'